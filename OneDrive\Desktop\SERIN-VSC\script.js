// حالة المحادثات
let conversations = [];
let currentConversationId = null;
const codeContexts = {};
let monacoEditor = null;
let activeFileId = null;

// نظام الملفات
let workspace = {
    files: {},
    folders: {
        'root': {
            id: 'root',
            name: 'root',
            path: '/',
            type: 'folder',
            children: []
        }
    },
    currentPath: '/'
};

// وظائف نظام الملفات
function createFile(name, content, language, path = '/') {
    // تنظيف المسار
    path = path.replace(/\/+/g, '/');
    if (!path.endsWith('/')) path += '/';
    if (!path.startsWith('/')) path = '/' + path;

    // التحقق من وجود ملف بنفس الاسم في نفس المجلد وإضافة رقم إذا كان موجودًا
    let finalName = name;
    let counter = 1;

    // البحث عن الملفات الموجودة بنفس الاسم في نفس المجلد
    const filesInFolder = Object.values(workspace.files).filter(file =>
        file.path.startsWith(path) && file.path.substring(path.length) === name
    );

    // إذا وجد ملف بنفس الاسم، نضيف رقم للملف الجديد
    if (filesInFolder.length > 0) {
        // استخراج اسم الملف واللاحقة
        const lastDotIndex = name.lastIndexOf('.');
        const baseName = lastDotIndex !== -1 ? name.substring(0, lastDotIndex) : name;
        const extension = lastDotIndex !== -1 ? name.substring(lastDotIndex) : '';

        // البحث عن أعلى رقم موجود في الملفات المتشابهة
        const regex = new RegExp(`^${escapeRegExp(baseName)} \\((\\d+)\\)${escapeRegExp(extension)}$`);

        Object.values(workspace.files).forEach(file => {
            if (file.path.startsWith(path)) {
                const fileName = file.path.substring(path.length);
                const match = fileName.match(regex);
                if (match) {
                    const num = parseInt(match[1]);
                    if (num >= counter) {
                        counter = num + 1;
                    }
                }
            }
        });

        // إنشاء اسم الملف الجديد مع الرقم
        finalName = `${baseName} (${counter})${extension}`;
    }

    // إضافة تعليق الملف إذا لم يكن موجودًا
    content = ensureFileComment(content);

    const fileId = 'file_' + Date.now() + Math.random().toString(36).substr(2, 5);
    const filePath = path + finalName;

    workspace.files[fileId] = {
        id: fileId,
        name: finalName,
        path: filePath,
        content: content,
        language: language,
        type: 'file'
    };

    // إضافة الملف إلى المجلد مع التحقق من وجود المجلد
    if (!workspace.folders[path]) {
        createFolder(path);
    }
    
    // تحقق مرة أخرى من وجود المجلد بعد محاولة إنشائه
    if (workspace.folders[path] && workspace.folders[path].children) {
        workspace.folders[path].children.push(fileId);
    } else {
        // إذا لم يكن المجلد موجودًا، أضف الملف إلى المجلد الجذر
        if (!workspace.folders['root']) {
            workspace.folders['root'] = {
                id: 'root',
                name: 'root',
                path: '/',
                type: 'folder',
                children: []
            };
        }
        if (!workspace.folders['root'].children) {
            workspace.folders['root'].children = [];
        }
        workspace.folders['root'].children.push(fileId);
    }

    // حفظ التغييرات مباشرة
    saveWorkspace();

    return fileId;
}

function ensureFileComment(content) {
    // احذف أي تعليق مسار في بداية الملف (JS, Python, HTML, CSS)
    content = content.replace(/^\s*(\/\/|#)\s*file:.*\n?/i, ''); // JS/Python
    content = content.replace(/^\s*<!--\s*file:.*?-->\s*\n?/i, ''); // HTML
    content = content.replace(/^\s*\/\*\s*file:.*?\*\/\s*\n?/is, ''); // CSS/JS block
        return content;
}

function createFolder(path) {
    const parts = path.split('/').filter(p => p);
    let currentPath = '/';

    for (const part of parts) {
        const newPath = currentPath + part + '/';
        if (!workspace.folders[newPath]) {
            const folderId = 'folder_' + Date.now() + Math.random().toString(36).substr(2, 5);
            workspace.folders[newPath] = {
                id: folderId,
                name: part,
                path: newPath,
                type: 'folder',
                children: []
            };

            // إضافة المجلد إلى المجلد الأب مع التحقق
            if (currentPath !== '/') {
                if (workspace.folders[currentPath] && workspace.folders[currentPath].children) {
                    workspace.folders[currentPath].children.push(folderId);
                }
            } else if (workspace.folders['root'] && workspace.folders['root'].children) {
                workspace.folders['root'].children.push(folderId);
            }
        }
        currentPath = newPath;
    }

    // حفظ التغييرات مباشرة
    saveWorkspace();
}

function updateFileExplorer() {
    const explorerContent = document.getElementById('explorer-content');
    explorerContent.innerHTML = '';

    // Breadcrumb Navigation
    const breadcrumb = document.createElement('div');
    breadcrumb.className = 'breadcrumb';

    const paths = workspace.currentPath.split('/').filter(p => p);
    let currentPath = '/';

    breadcrumb.innerHTML = `<span class="breadcrumb-item" onclick="navigateToFolder('/')">root</span>`;

    paths.forEach((part) => {
        currentPath += part + '/';
        breadcrumb.innerHTML += `
                    <span class="breadcrumb-separator">/</span>
                    <span class="breadcrumb-item" onclick="navigateToFolder('${currentPath}')">${part}</span>
                `;
    });

    explorerContent.appendChild(breadcrumb);

    // Explorer Sections - VS Code style
    const openEditorsSection = document.createElement('div');
    openEditorsSection.className = 'explorer-section';
    openEditorsSection.innerHTML = `
                <div class="explorer-section-header">
                    <span>المحررات المفتوحة</span>
                    <div class="explorer-section-actions">
                        <button class="explorer-section-action" title="حفظ الكل"><i class="fas fa-save"></i></button>
                        <button class="explorer-section-action" title="إغلاق الكل" onclick="closeAllFiles()"><i class="fas fa-times"></i></button>
                    </div>
                </div>
                <div class="explorer-section-content" id="open-editors-content"></div>
            `;
    explorerContent.appendChild(openEditorsSection);

    // Populate Open Editors section - عرض جميع الملفات المفتوحة وليس فقط الملف النشط
    const openEditorsContent = document.getElementById('open-editors-content');
    if (openEditorsContent) {
        // الحصول على قائمة الملفات المفتوحة
        const openFiles = JSON.parse(localStorage.getItem('openFiles') || '[]');

        if (openFiles.length > 0) {
            openFiles.forEach(fileId => {
                if (workspace.files[fileId]) {
                    const file = workspace.files[fileId];
                    const fileItem = document.createElement('div');
                    fileItem.className = `explorer-item file ${activeFileId === fileId ? 'active' : ''}`;

                    // تحقق مما إذا كان المحرر مفتوحًا حاليًا
                    const isEditorVisible = document.getElementById('code-executor').classList.contains('visible');

                    fileItem.innerHTML = `
                                <div class="explorer-item-content">
                                    <span class="explorer-item-icon">
                                        ${getFileIcon(file.name)}
                                    </span>
                                    <span class="explorer-item-name">${file.name}</span>
                                </div>
                                <div class="explorer-item-actions">
                                    ${!isEditorVisible && activeFileId === fileId ?
                            `<button class="explorer-item-action edit-action" onclick="reopenEditor(event)" title="إعادة فتح المحرر">
                                            <i class="fas fa-external-link-alt"></i>
                                        </button>` : ''
                        }
                                    <button class="explorer-item-action delete-action" onclick="closeFile('${file.id}', event)" title="إغلاق">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                            `;
                    fileItem.onclick = (e) => {
                        if (!e.target.closest('.explorer-item-action')) {
                            // تنشيط هذا الملف
                            activeFileId = file.id;
                            updateFileTabs();

                            // إذا كان المحرر مغلقًا والملف هو النشط، نعيد فتحه
                            if (!isEditorVisible && activeFileId === fileId) {
                                reopenEditor();
                            } else {
                                // إذا كان المحرر مفتوحًا أو الملف ليس هو النشط، نفتح الملف
                                openFile(file.id);
                            }
                        }
                    };
                    openEditorsContent.appendChild(fileItem);
                }
            });
        } else {
            // إذا لم يكن هناك ملف مفتوح، نعرض رسالة
            const emptyMessage = document.createElement('div');
            emptyMessage.className = 'empty-editors-message';
            emptyMessage.innerHTML = `<span style="padding: 8px; color: var(--text-dim); font-size: 12px; display: block;">لا توجد ملفات مفتوحة</span>`;
            openEditorsContent.appendChild(emptyMessage);
        }
    }

    // Project Files Section
    const projectSection = document.createElement('div');
    projectSection.className = 'explorer-section';
    projectSection.innerHTML = `
                <div class="explorer-section-header">
                    <span>${paths.length > 0 ? paths[paths.length - 1].toUpperCase() : 'مساحة العمل'}</span>
                    <div class="explorer-section-actions">
                        <button class="explorer-section-action" onclick="createNewFile()" title="ملف جديد"><i class="fas fa-file"></i></button>
                        <button class="explorer-section-action" onclick="createNewFolder()" title="مجلد جديد"><i class="fas fa-folder"></i></button>
                        <button class="explorer-section-action" onclick="refreshFileExplorer()" title="تحديث المستكشف"><i class="fas fa-sync"></i></button>
                    </div>
                </div>
                <div class="explorer-section-content" id="project-files-content"></div>
            `;
    explorerContent.appendChild(projectSection);

    // Populate Project Files section
    const projectFilesContent = document.getElementById('project-files-content');
    const currentFolder = workspace.folders[workspace.currentPath] || workspace.folders['root'];

    if (currentFolder && Array.isArray(currentFolder.children)) {
        // تحقق من وجود أي ملفات أو مجلدات في المسار الحالي
        if (currentFolder.children.length === 0) {
            const emptyMessage = document.createElement('div');
            emptyMessage.className = 'empty-folder-message';
            emptyMessage.innerHTML = `<span style="padding: 8px; color: var(--text-dim); font-size: 12px; display: block;">المجلد فارغ</span>`;
            projectFilesContent.appendChild(emptyMessage);
            return;
        }

        // Sort: Folders first, then files alphabetically
        const sortedItems = [...currentFolder.children].sort((a, b) => {
            const isAFolder = a.startsWith('folder_');
            const isBFolder = b.startsWith('folder_');

            if (isAFolder && !isBFolder) return -1;
            if (!isAFolder && isBFolder) return 1;

            // Sort by name if both are folders or both are files
            const itemA = isAFolder ?
                Object.values(workspace.folders).find(f => f && f.id === a) :
                workspace.files[a];
            const itemB = isBFolder ?
                Object.values(workspace.folders).find(f => f && f.id === b) :
                workspace.files[b];

            if (itemA && itemB) {
                return itemA.name.localeCompare(itemB.name);
            }
            return 0;
        });

        sortedItems.forEach(childId => {
            let item;
            if (childId.startsWith('folder_')) {
                const folderObj = Object.values(workspace.folders).find(f => f && f.id === childId);
                item = folderObj ? workspace.folders[folderObj.path] : null;

                // تحقق إضافي من صحة المجلد
                if (!item) {
                    console.warn('تم العثور على معرف مجلد غير صالح:', childId);
                    return;
                }
            } else {
                item = workspace.files[childId];

                // تحقق من وجود الملف
                if (!item) {
                    console.warn('تم العثور على معرف ملف غير صالح:', childId);
                    return;
                }
            }

            const itemElement = document.createElement('div');
            itemElement.className = `explorer-item ${item.type} ${activeFileId === item.id ? 'active' : ''}`;
            itemElement.innerHTML = `
                        <div class="explorer-item-content">
                            <span class="explorer-item-icon" style="order: -1">
                                ${item.type === 'folder' ? '<i class="fas fa-folder"></i>' : getFileIcon(item.name)}
                        </span>
                        <span class="explorer-item-name">${item.name}</span>
                        </div>
                        <div class="explorer-item-actions">
                            <button class="explorer-item-action delete-action" onclick="deleteFile('${item.id}', event)" title="حذف">
                                <i class="fas fa-trash"></i>
                            </button>
                            ${item.type === 'file' ? `
                            <button class="explorer-item-action edit-action" onclick="renameFile('${item.id}', event)" title="إعادة تسمية">
                                <i class="fas fa-edit"></i>
                            </button>
                            ` : ''}
                        </div>
                    `;

            itemElement.onclick = (e) => {
                if (!e.target.closest('.explorer-item-action')) {
                    if (item.type === 'folder') {
                        navigateToFolder(item.path);
                    } else {
                        openFile(item.id);
                    }
                }
            };

            projectFilesContent.appendChild(itemElement);
        });
    } else {
        // إذا كان المجلد غير موجود، نعرض رسالة
        console.warn('المجلد غير موجود:', workspace.currentPath);
        const emptyMessage = document.createElement('div');
        emptyMessage.className = 'empty-folder-message';
        emptyMessage.innerHTML = `<span style="padding: 8px; color: var(--text-dim); font-size: 12px; display: block;">المجلد غير موجود</span>`;
        projectFilesContent.appendChild(emptyMessage);
    }
}

// Helper function to get appropriate file icon based on extension
function getFileIcon(filename) {
    const extension = filename.split('.').pop().toLowerCase();
    const iconMap = {
        'html': '<i class="fas fa-file-code" style="color: #e44d26;"></i>',
        'css': '<i class="fas fa-file-code" style="color: #264de4;"></i>',
        'js': '<i class="fas fa-file-code" style="color: #f7df1e;"></i>',
        'json': '<i class="fas fa-file-code" style="color: #f7df1e;"></i>',
        'ts': '<i class="fas fa-file-code" style="color: #007acc;"></i>',
        'py': '<i class="fas fa-file-code" style="color: #306998;"></i>',
        'php': '<i class="fas fa-file-code" style="color: #777bb4;"></i>',
        'md': '<i class="fas fa-file-alt" style="color: #03a9f4;"></i>',
        'txt': '<i class="fas fa-file-alt" style="color: #9e9e9e;"></i>',
        'jpg': '<i class="fas fa-file-image" style="color: #4caf50;"></i>',
        'jpeg': '<i class="fas fa-file-image" style="color: #4caf50;"></i>',
        'png': '<i class="fas fa-file-image" style="color: #4caf50;"></i>',
        'gif': '<i class="fas fa-file-image" style="color: #4caf50;"></i>',
        'svg': '<i class="fas fa-file-image" style="color: #ff9800;"></i>',
        'pdf': '<i class="fas fa-file-pdf" style="color: #f44336;"></i>',
        'zip': '<i class="fas fa-file-archive" style="color: #ffc107;"></i>',
        'rar': '<i class="fas fa-file-archive" style="color: #ffc107;"></i>'
    };

    return iconMap[extension] || '<i class="fas fa-file-code" style="color: #75beff;"></i>';
}



function navigateToFolder(path) {
    // تحديث وقت آخر تفاعل مع المستكشف
    if (typeof lastExplorerInteraction !== 'undefined') {
        lastExplorerInteraction = Date.now();
    }

    // تنظيف المسار
    if (!path.startsWith('/')) {
        path = '/' + path;
    }
    if (!path.endsWith('/')) {
        path += '/';
    }

    // تأكد من وجود المجلد
    if (path !== '/' && !workspace.folders[path]) {
        console.error('المجلد غير موجود:', path);
        // إذا كان المجلد غير موجود، نعود للمجلد الأب
        const parentPath = path.substring(0, path.lastIndexOf('/', path.length - 2) + 1);
        if (parentPath && workspace.folders[parentPath]) {
            path = parentPath;
        } else {
            path = '/';
        }
    }

    workspace.currentPath = path;

    // حفظ المسار الحالي في التخزين المحلي
    localStorage.setItem('currentPath', path);

    updateFileExplorer();
}

// Add the VSCode-style status bar
function addStatusBar() {
    // Check if status bar already exists
    if (document.querySelector('.status-bar')) return;

    const codeExecutor = document.getElementById('code-executor');
    if (!codeExecutor) return;

    const statusBar = document.createElement('div');
    statusBar.className = 'status-bar';
    statusBar.innerHTML = `
                <div class="status-items-left">
            <div class="status-item icon-only-on-small">
                <i class="fas fa-code-branch"></i>
                <span class="status-item-text">main</span>
            </div>
            <div class="status-item hide-on-small">
                <i class="fas fa-sync"></i>
            </div>
            <div class="status-item terminal-toggle-btn">
                <i class="fas fa-terminal"></i>
                <span class="status-item-text">Terminal</span>
            </div>
            <div class="status-item hide-on-tiny">
                <i class="fas fa-bell"></i>
            </div>
                </div>
                <div class="status-items-right">
            <div class="status-item cursor-position always-show-text">
                <span class="status-item-text">Ln 1, Col 1</span>
            </div>
            <div class="status-item indent-setting icon-only-on-small">
                <i class="fas fa-indent"></i>
                <span class="status-item-text">Spaces: 4</span>
            </div>
            <div class="status-item hide-on-small">UTF-8</div>
            <div class="status-item language-indicator">
                <i class="fas fa-file-code"></i>
                <span class="status-item-text">JavaScript</span>
            </div>
            <div class="status-item hide-on-small">
                <i class="fas fa-check-circle"></i>
                <span class="status-item-text">Prettier</span>
            </div>
                </div>
            `;
    codeExecutor.appendChild(statusBar);

    // Make status items interactive
    statusBar.querySelectorAll('.status-item').forEach(item => {
        item.addEventListener('click', function() {
            // Show a tooltip or perform an action when clicked
            if (this.classList.contains('indent-setting')) {
                const options = ['Spaces: 2', 'Spaces: 4', 'Tabs: 4'];
                const currentIndex = options.findIndex(opt => {
                    const text = this.querySelector('.status-item-text');
                    return text && opt === text.textContent;
                });
                const nextIndex = (currentIndex + 1) % options.length;
                const text = this.querySelector('.status-item-text');
                if (text) {
                    text.textContent = options[nextIndex];
                }

                // Also update editor if available
                if (monacoEditor && monacoEditor.updateOptions) {
                    const tabSize = parseInt(options[nextIndex].split(':')[1]);
                    monacoEditor.updateOptions({
                        tabSize,
                        insertSpaces: options[nextIndex].startsWith('Spaces')
                    });
                }
            } else if (this.classList.contains('terminal-toggle-btn')) {
                // Toggle terminal visibility
                const executorFooter = document.querySelector('.executor-footer');
                if (executorFooter) {
                    if (executorFooter.classList.contains('hidden')) {
                        executorFooter.classList.remove('hidden');
                        localStorage.setItem('terminalState', 'open');
                    } else if (executorFooter.classList.contains('collapsed')) {
                        executorFooter.classList.remove('collapsed');
                        localStorage.setItem('terminalState', 'open');
                    } else {
                        executorFooter.classList.add('hidden');
                        localStorage.setItem('terminalState', 'hidden');
                    }
                }
            }
        });
    });

    // Update the language indicator based on current file
    const currentFile = workspace.files[activeFileId];
    if (currentFile && currentFile.language) {
        const langIndicator = statusBar.querySelector('.language-indicator .status-item-text');
        if (langIndicator) {
            langIndicator.textContent = currentFile.language.charAt(0).toUpperCase() + currentFile.language.slice(1);
        }
    }

    // Adjust status bar for screen size
    updateStatusBarResponsiveness();

    // Add window resize listener for responsive status bar
    window.addEventListener('resize', updateStatusBarResponsiveness);
}

// Function to update status bar based on screen width
function updateStatusBarResponsiveness() {
    const statusBar = document.querySelector('.status-bar');
    if (!statusBar) return;

    const width = window.innerWidth;

    // Very small screens - show only essential items
    if (width < 400) {
        statusBar.querySelectorAll('.status-item:not(.always-show-text)').forEach(item => {
            const text = item.querySelector('.status-item-text');
            if (text) text.style.display = 'none';
        });
    }
    // Small screens - show icons and some text
    else if (width < 600) {
        statusBar.querySelectorAll('.status-item.icon-only-on-small .status-item-text').forEach(text => {
            text.style.display = 'none';
        });
        statusBar.querySelectorAll('.status-item:not(.icon-only-on-small):not(.hide-on-small) .status-item-text').forEach(text => {
            text.style.display = '';
        });
    }
    // Larger screens - show everything
    else {
        statusBar.querySelectorAll('.status-item-text').forEach(text => {
            text.style.display = '';
        });
    }
}

function openFile(fileId) {
    // تحديث وقت آخر تفاعل مع المستكشف
    if (typeof lastExplorerInteraction !== 'undefined') {
        lastExplorerInteraction = Date.now();
    }

    const file = workspace.files[fileId];
    if (!file) return;

    // تحديث الملف النشط
    activeFileId = fileId;

    // إضافة الملف إلى قائمة الملفات المفتوحة إذا لم يكن موجودًا
    const openFiles = JSON.parse(localStorage.getItem('openFiles') || '[]');
    if (!openFiles.includes(fileId)) {
        openFiles.push(fileId);
        localStorage.setItem('openFiles', JSON.stringify(openFiles));
    }

    const editorContainer = document.getElementById('editor-container');

    if (typeof monaco === 'undefined') {
        console.error('Monaco Editor not loaded. Please wait...');
        editorContainer.innerHTML = '<div style="padding: 20px; color: #fff;">Loading editor...</div>';
        setTimeout(() => openFile(fileId), 1000);
        return;
    }

    if (!monacoEditor) {
        try {
            const model = monaco.editor.createModel(
                file.content || '',
                file.language || 'plaintext'
            );

            // VS Code-like options
            monacoEditor = monaco.editor.create(editorContainer, {
                model: model,
                theme: 'vs-dark',
                automaticLayout: true,
                fontSize: 14,
                lineNumbers: 'on',
                minimap: { enabled: true },
                scrollBeyondLastLine: false,
                renderLineHighlight: 'all',
                cursorBlinking: 'smooth',
                cursorSmoothCaretAnimation: true,
                smoothScrolling: true,
                wordWrap: 'on',
                formatOnPaste: true,
                formatOnType: true,
                suggest: {
                    showMethods: true,
                    showFunctions: true,
                    showConstructors: true,
                    showFields: true,
                    showVariables: true,
                    showClasses: true,
                    showStructs: true,
                    showInterfaces: true,
                    showModules: true,
                    showProperties: true,
                    showEvents: true,
                    showOperators: true,
                    showUnits: true,
                    showValues: true,
                    showConstants: true,
                    showEnums: true,
                    showEnumMembers: true,
                    showKeywords: true,
                    showWords: true,
                    showColors: true,
                    showFiles: true,
                    showReferences: true,
                    showFolders: true,
                    showTypeParameters: true,
                    showIssues: true,
                    showUsers: true,
                    showSnippets: true
                }
            });

            // Set direction to LTR for code
            if (monacoEditor.updateOptions) {
                monacoEditor.updateOptions({ direction: 'ltr' });
            }

            if (monacoEditor.getDomNode) {
                monacoEditor.getDomNode().style.direction = 'ltr';
            }

            // Update content when changed
            if (monacoEditor.onDidChangeModelContent) {
                // إزالة المستمعين السابقة لتجنب التكرار
                if (monacoEditor.getModel()._contentChangedHandler) {
                    monacoEditor.getModel()._contentChangedHandler.dispose();
                }

                monacoEditor.getModel()._contentChangedHandler = monacoEditor.onDidChangeModelContent(function () {
                    // تأكد من أن التغييرات تُحفظ فقط للملف النشط الحالي
                    if (activeFileId && workspace.files[activeFileId] && activeFileId === fileId) {
                        workspace.files[activeFileId].content = monacoEditor.getValue();
                        // حفظ التغييرات عند تعديل المحتوى
                        saveWorkspace();
                    }
                });
            }

            // Add cursor position tracking (VS Code status bar)
            monacoEditor.onDidChangeCursorPosition(function (e) {
                const statusItems = document.querySelectorAll('.status-items-right .status-item');
                if (statusItems.length > 0) {
                    statusItems[0].textContent = `Ln ${e.position.lineNumber}, Col ${e.position.column}`;
                }
            });

            // Add quick action buttons (like VS Code)
            const quickActions = document.createElement('div');
            quickActions.className = 'quick-actions';
            quickActions.innerHTML = `
                        <div class="quick-action" title="Split Editor"><i class="fas fa-columns"></i></div>
                        <div class="quick-action" title="More Options"><i class="fas fa-ellipsis-v"></i></div>
                    `;
            editorContainer.appendChild(quickActions);

        } catch (e) {
            console.error('Error initializing Monaco Editor:', e);
            editorContainer.innerHTML = '<div style="padding: 20px; color: #fff;">Error loading editor: ' + e.message + '</div>';
        }
    } else {
        try {
            // تحقق من وجود النماذج
            let model = null;
            const modelUri = monaco.Uri.parse('inmemory://' + fileId);

            // البحث عن النموذج الحالي
            const existingModels = monaco.editor.getModels();
            model = existingModels.find(m => m.uri && m.uri.toString() === modelUri.toString());

            // إنشاء نموذج جديد إذا لم يكن موجودًا
            if (!model) {
                try {
                    model = monaco.editor.createModel(
                        file.content || '',
                        file.language || 'plaintext',
                        modelUri
                    );
                } catch (e) {
                    console.warn('Error creating model, trying to reuse existing model:', e);
                    // محاولة استخدام النموذج الحالي للمحرر
                    model = monacoEditor.getModel();
                    if (model) {
                        model.setValue(file.content || '');
                        try {
                            if (file.language) {
                                monaco.editor.setModelLanguage(model, file.language);
                            }
                        } catch (langError) {
                            console.warn('Could not set language:', langError);
                        }
                    } else {
                        // إنشاء نموذج جديد بدون URI محدد
                        model = monaco.editor.createModel(file.content || '');
                    }
                }
            } else {
                // تحديث النموذج الموجود
                model.setValue(file.content || '');
                try {
                    if (file.language) {
                        monaco.editor.setModelLanguage(model, file.language);
                    }
                } catch (langError) {
                    console.warn('Could not set language:', langError);
                }
            }

            // تعيين النموذج للمحرر
            monacoEditor.setModel(model);

            // تحديث مؤشر اللغة في شريط الحالة
            try {
                if (file.language) {
                    const statusItems = document.querySelectorAll('.status-items-right .status-item');
                    if (statusItems.length > 3) {
                        statusItems[3].textContent = file.language.charAt(0).toUpperCase() + file.language.slice(1);
                    }
                }
            } catch (e) {
                console.warn('Could not update language indicator:', e);
            }

            // تسجيل حدث تغيير المحتوى للحفظ التلقائي
            if (monacoEditor.onDidChangeModelContent) {
                // إزالة المستمعين السابقة لتجنب التكرار
                if (monacoEditor.getModel()._contentChangedHandler) {
                    monacoEditor.getModel()._contentChangedHandler.dispose();
                }

                monacoEditor.getModel()._contentChangedHandler = monacoEditor.onDidChangeModelContent(function () {
                    // تأكد من أن التغييرات تُحفظ فقط للملف النشط الحالي
                    if (activeFileId && workspace.files[activeFileId] && activeFileId === fileId) {
                        workspace.files[activeFileId].content = monacoEditor.getValue();
                        // حفظ التغييرات عند تعديل المحتوى
                        saveWorkspace();
                    }
                });
            }
        } catch (e) {
            console.error('Error changing editor model:', e);
        }
    }

    updateFileTabs();
    document.getElementById('code-executor').classList.add('visible');

    // Add VS Code status bar
    addStatusBar();

    // Focus editor after opening
    setTimeout(() => {
        if (monacoEditor && monacoEditor.focus) {
            try {
                monacoEditor.focus();
            } catch (e) {
                console.warn('Could not focus editor:', e);
            }
        }
    }, 100);
}

async function renameFile(fileId, event) {
    // تحديث وقت آخر تفاعل مع المستكشف
    if (typeof lastExplorerInteraction !== 'undefined') {
        lastExplorerInteraction = Date.now();
    }

    event.stopPropagation();
    const file = workspace.files[fileId];
    if (!file) return;

    const newName = await showTextInput({
        title: 'إعادة تسمية الملف',
        label: 'اسم الملف الجديد:',
        placeholder: 'أدخل اسم الملف...',
        defaultValue: file.name,
        icon: '📝',
        validation: (value) => {
            if (!value.trim()) return false;
            if (value.includes('/') || value.includes('\\')) return false;
            return true;
        }
    });

    // تحديث الوقت بعد إغلاق النافذة (سواء تم التعديل أو الإلغاء)
    if (typeof lastExplorerInteraction !== 'undefined') {
        lastExplorerInteraction = Date.now();
    }

    if (newName && newName !== file.name) {
        file.name = newName;

        // تحديث مسار الملف أيضًا
        const pathParts = file.path.split('/');
        pathParts.pop(); // إزالة اسم الملف القديم
        pathParts.push(newName); // إضافة اسم الملف الجديد
        file.path = pathParts.join('/');

        updateFileExplorer();

        // حفظ التغييرات مباشرة
        saveWorkspace();
    }
}

async function deleteFile(itemId, event) {
    // تحديث وقت آخر تفاعل مع المستكشف
    if (typeof lastExplorerInteraction !== 'undefined') {
        lastExplorerInteraction = Date.now();
    }

    event.stopPropagation();

    const confirmed = await showConfirm(
        'هل أنت متأكد من حذف هذا العنصر؟ لا يمكن التراجع عن هذا الإجراء.',
        'تأكيد الحذف'
    );

    // تحديث الوقت بعد إغلاق النافذة (سواء تم الحذف أو الإلغاء)
    if (typeof lastExplorerInteraction !== 'undefined') {
        lastExplorerInteraction = Date.now();
    }

    if (!confirmed) return;

    if (itemId.startsWith('folder_')) {
        const folderObj = Object.values(workspace.folders).find(f => f && f.id === itemId);
        if (folderObj) {
            // حذف جميع الملفات والمجلدات داخل هذا المجلد
            const folderPath = folderObj.path;

            // حذف الملفات داخل المجلد
            for (const fileId in workspace.files) {
                const file = workspace.files[fileId];
                if (file.path.startsWith(folderPath)) {
                    delete workspace.files[fileId];

                    // إزالة من قائمة الملفات المفتوحة
                    if (activeFileId === fileId) {
                        hideCodeExecutor();
                    }
                    const openFiles = JSON.parse(localStorage.getItem('openFiles') || '[]');
                    const updatedOpenFiles = openFiles.filter(id => id !== fileId);
                    localStorage.setItem('openFiles', JSON.stringify(updatedOpenFiles));
                }
            }

            // حذف المجلدات الفرعية
            for (const path in workspace.folders) {
                if (path !== folderPath && path.startsWith(folderPath)) {
                    delete workspace.folders[path];
                }
            }

            // حذف المجلد نفسه
            delete workspace.folders[folderPath];
        }
    } else {
        // إذا كان الملف المحذوف هو الملف المفتوح حاليًا، نغلق المحرر
        if (activeFileId === itemId) {
            hideCodeExecutor();
        }

        delete workspace.files[itemId];

        // حذف الملف من قائمة الملفات المفتوحة إذا كان موجودًا
        const openFiles = JSON.parse(localStorage.getItem('openFiles') || '[]');
        const updatedOpenFiles = openFiles.filter(id => id !== itemId);
        localStorage.setItem('openFiles', JSON.stringify(updatedOpenFiles));
    }

    // إزالة العنصر من المجلد الأب مع التحقق
    for (const folder of Object.values(workspace.folders)) {
        if (folder && Array.isArray(folder.children)) {
            const index = folder.children.indexOf(itemId);
            if (index !== -1) {
                folder.children.splice(index, 1);
                break;
            }
        }
    }

    updateFileExplorer();
    updateFileTabs();

    // حفظ التغييرات مباشرة
    saveWorkspace();
}

function toggleExplorer() {
    const explorer = document.getElementById('file-explorer');
    const toggle = document.getElementById('explorer-toggle');

    explorer.classList.toggle('visible');
    toggle.classList.toggle('active');
}

async function createNewFile() {
    // تحديث وقت آخر تفاعل مع المستكشف
    if (typeof lastExplorerInteraction !== 'undefined') {
        lastExplorerInteraction = Date.now();
    }

    const fileName = await showTextInput({
        title: 'إنشاء ملف جديد',
        label: 'اسم الملف:',
        placeholder: 'مثال: index.html, script.js, style.css',
        icon: '📄',
        suggestions: [
            'index.html',
            'script.js',
            'style.css',
            'README.md',
            'package.json',
            'main.py',
            'app.js',
            'index.php'
        ],
        validation: (value) => {
            if (!value.trim()) return false;
            if (value.includes('/') || value.includes('\\')) return false;
            if (!value.includes('.')) return false;
            return true;
        }
    });

    // تحديث الوقت بعد إغلاق النافذة (سواء تم الإنشاء أو الإلغاء)
    if (typeof lastExplorerInteraction !== 'undefined') {
        lastExplorerInteraction = Date.now();
    }

    if (fileName) {
        const lang = fileName.split('.').pop();
        const fileId = createFile(fileName, '', lang, workspace.currentPath);
        openFile(fileId);
        updateFileExplorer();

        // إظهار إشعار نجاح
        showSuccess(`تم إنشاء الملف "${fileName}" بنجاح!`, 'ملف جديد');
    }
}

async function createNewFolder() {
    // تحديث وقت آخر تفاعل مع المستكشف
    if (typeof lastExplorerInteraction !== 'undefined') {
        lastExplorerInteraction = Date.now();
    }

    const folderName = await showTextInput({
        title: 'إنشاء مجلد جديد',
        label: 'اسم المجلد:',
        placeholder: 'أدخل اسم المجلد...',
        icon: '📁',
        suggestions: [
            'src',
            'assets',
            'components',
            'utils',
            'styles',
            'images',
            'docs',
            'tests'
        ],
        validation: (value) => {
            if (!value.trim()) return false;
            if (value.includes('/') || value.includes('\\')) return false;
            return true;
        }
    });

    // تحديث الوقت بعد إغلاق النافذة (سواء تم الإنشاء أو الإلغاء)
    if (typeof lastExplorerInteraction !== 'undefined') {
        lastExplorerInteraction = Date.now();
    }

    if (folderName) {
        // تحقق من صحة اسم المجلد
        if (folderName.includes('/')) {
            showError('اسم المجلد لا يمكن أن يحتوي على الرمز "/"', 'خطأ في اسم المجلد');
            return;
        }

        // إنشاء المسار الجديد
        let newPath = workspace.currentPath;
        if (!newPath.endsWith('/')) newPath += '/';
        newPath += folderName + '/';

        // تحقق من عدم وجود مجلد بنفس الاسم
        if (workspace.folders[newPath]) {
            showWarning('يوجد مجلد بنفس الاسم بالفعل!', 'مجلد موجود');
            return;
        }

        // إنشاء المجلد
        createFolder(newPath);

        // تحديث المستكشف
        updateFileExplorer();

        // الانتقال إلى المجلد الجديد
        navigateToFolder(newPath);

        // إظهار إشعار نجاح
        showSuccess(`تم إنشاء المجلد "${folderName}" بنجاح!`, 'مجلد جديد');
    }
}

function uploadFile() {
    // تحديث وقت آخر تفاعل مع المستكشف
    if (typeof lastExplorerInteraction !== 'undefined') {
        lastExplorerInteraction = Date.now();
    }

    document.getElementById('file-upload').click();
}

function handleFileUpload(event) {
    const files = event.target.files;
    if (!files.length) return;

    Array.from(files).forEach(file => {
        const reader = new FileReader();
        reader.onload = (e) => {
            const content = e.target.result;
            const lang = file.name.split('.').pop();
            createFile(file.name, content, lang, workspace.currentPath);
            updateFileExplorer();

            // إظهار إشعار نجاح
            showSuccess(`تم رفع الملف "${file.name}" بنجاح!`, 'رفع ملف');
        };
        reader.readAsText(file);
    });
}

// متغير لتخزين الملفات المرفوعة في المحادثة
let uploadedChatFiles = [];

// فتح نافذة رفع الملفات للمحادثة
function openChatFileUpload() {
    document.getElementById('chat-file-upload').click();
}

// معالجة رفع الملفات في المحادثة
async function handleChatFileUpload(event) {
    const files = event.target.files;
    if (!files.length) return;

    // إخفاء شاشة الترحيب إذا كانت ظاهرة
    hideWelcomeScreen();

    // إضافة رسالة المستخدم مع الملفات
    const fileNames = Array.from(files).map(f => f.name).join(', ');
    addMessage(`تم رفع الملفات: ${fileNames}`, 'user');

    // معالجة كل ملف
    for (const file of files) {
        try {
            const fileData = await processUploadedFile(file);
            uploadedChatFiles.push(fileData);
        } catch (error) {
            console.error('خطأ في معالجة الملف:', file.name, error);
            addMessage(`خطأ في معالجة الملف "${file.name}": ${error.message}`, 'bot', true);
        }
    }

    // عرض الملفات المرفوعة
    if (uploadedChatFiles.length > 0) {
        displayUploadedFiles();
    }

    // مسح قيمة input
    event.target.value = '';
}

// معالجة الملف المرفوع حسب نوعه
async function processUploadedFile(file) {
    const fileExtension = file.name.split('.').pop().toLowerCase();
    const fileSize = formatFileSize(file.size);

    const fileData = {
        id: 'file_' + Date.now() + Math.random().toString(36).substr(2, 5),
        name: file.name,
        size: fileSize,
        type: fileExtension,
        originalFile: file,
        content: '',
        preview: '',
        processed: false
    };

    try {
        // معالجة حسب نوع الملف
        switch (fileExtension) {
            case 'pdf':
                await processPDFFile(file, fileData);
                break;
            case 'docx':
                await processDOCXFile(file, fileData);
                break;
            case 'pptx':
                await processPPTXFile(file, fileData);
                break;
            case 'txt':
            case 'md':
            case 'json':
            case 'xml':
            case 'html':
            case 'css':
            case 'js':
            case 'py':
            case 'java':
            case 'cpp':
            case 'c':
            case 'h':
            case 'cs':
            case 'php':
            case 'rb':
            case 'go':
            case 'rs':
            case 'swift':
            case 'kt':
            case 'ts':
            case 'jsx':
            case 'tsx':
            case 'vue':
            case 'scss':
            case 'sass':
            case 'less':
            case 'yaml':
            case 'yml':
            case 'toml':
            case 'ini':
            case 'cfg':
            case 'conf':
            case 'log':
            case 'sql':
            case 'sh':
            case 'bat':
            case 'ps1':
                await processTextFile(file, fileData);
                break;
            case 'csv':
                await processCSVFile(file, fileData);
                break;
            case 'jpg':
            case 'jpeg':
            case 'png':
            case 'gif':
            case 'svg':
            case 'webp':
            case 'bmp':
                await processImageFile(file, fileData);
                break;
            default:
                // محاولة قراءة كملف نصي
                await processTextFile(file, fileData);
        }

        fileData.processed = true;
        return fileData;

    } catch (error) {
        console.error('خطأ في معالجة الملف:', error);
        fileData.content = 'خطأ في معالجة الملف: ' + error.message;
        fileData.processed = false;
        return fileData;
    }
}

// معالجة ملفات PDF
async function processPDFFile(file, fileData) {
    return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = async function(e) {
            try {
                const typedarray = new Uint8Array(e.target.result);
                const pdf = await pdfjsLib.getDocument(typedarray).promise;

                let fullText = '';
                for (let i = 1; i <= pdf.numPages; i++) {
                    const page = await pdf.getPage(i);
                    const textContent = await page.getTextContent();
                    const pageText = textContent.items.map(item => item.str).join(' ');
                    fullText += `صفحة ${i}:\n${pageText}\n\n`;
                }

                fileData.content = fullText;
                fileData.preview = fullText.substring(0, 200) + (fullText.length > 200 ? '...' : '');
                resolve();
            } catch (error) {
                reject(error);
            }
        };
        reader.onerror = () => reject(new Error('خطأ في قراءة ملف PDF'));
        reader.readAsArrayBuffer(file);
    });
}

// معالجة ملفات DOCX
async function processDOCXFile(file, fileData) {
    return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = async function(e) {
            try {
                const arrayBuffer = e.target.result;
                const result = await mammoth.extractRawText({arrayBuffer: arrayBuffer});

                fileData.content = result.value;
                fileData.preview = result.value.substring(0, 200) + (result.value.length > 200 ? '...' : '');
                resolve();
            } catch (error) {
                reject(error);
            }
        };
        reader.onerror = () => reject(new Error('خطأ في قراءة ملف DOCX'));
        reader.readAsArrayBuffer(file);
    });
}

// معالجة ملفات PPTX (مبسطة)
async function processPPTXFile(file, fileData) {
    return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = async function(e) {
            try {
                // معالجة مبسطة لملفات PPTX
                fileData.content = `ملف عرض تقديمي: ${file.name}\nالحجم: ${fileData.size}`;
                fileData.preview = `عرض تقديمي PowerPoint - ${file.name}`;
                resolve();
            } catch (error) {
                reject(error);
            }
        };
        reader.onerror = () => reject(new Error('خطأ في قراءة ملف PPTX'));
        reader.readAsArrayBuffer(file);
    });
}

// معالجة الملفات النصية
async function processTextFile(file, fileData) {
    return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = function(e) {
            try {
                const content = e.target.result;
                fileData.content = content;
                fileData.preview = content.substring(0, 200) + (content.length > 200 ? '...' : '');
                resolve();
            } catch (error) {
                reject(error);
            }
        };
        reader.onerror = () => reject(new Error('خطأ في قراءة الملف النصي'));
        reader.readAsText(file, 'UTF-8');
    });
}

// معالجة ملفات CSV
async function processCSVFile(file, fileData) {
    return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = function(e) {
            try {
                const csvText = e.target.result;
                const parsed = Papa.parse(csvText, { header: true });

                fileData.content = csvText;
                fileData.csvData = parsed.data;

                // إنشاء معاينة
                const preview = `ملف CSV - ${parsed.data.length} صف\nالأعمدة: ${Object.keys(parsed.data[0] || {}).join(', ')}\n\nأول 3 صفوف:\n${parsed.data.slice(0, 3).map(row => Object.values(row).join(' | ')).join('\n')}`;
                fileData.preview = preview;
                resolve();
            } catch (error) {
                reject(error);
            }
        };
        reader.onerror = () => reject(new Error('خطأ في قراءة ملف CSV'));
        reader.readAsText(file, 'UTF-8');
    });
}

// معالجة الصور
async function processImageFile(file, fileData) {
    return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = function(e) {
            try {
                const imageUrl = e.target.result;
                fileData.content = `صورة: ${file.name}\nالحجم: ${fileData.size}\nالنوع: ${file.type}`;
                fileData.imageUrl = imageUrl;
                fileData.preview = `صورة - ${file.name} (${fileData.size})`;
                resolve();
            } catch (error) {
                reject(error);
            }
        };
        reader.onerror = () => reject(new Error('خطأ في قراءة الصورة'));
        reader.readAsDataURL(file);
    });
}

// تنسيق حجم الملف
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// عرض الملفات المرفوعة
function displayUploadedFiles() {
    if (uploadedChatFiles.length === 0) return;

    const chatWindow = document.getElementById('chat-window');

    // إنشاء حاوية الملفات المرفوعة
    const filesContainer = document.createElement('div');
    filesContainer.className = 'uploaded-files-container';

    const header = document.createElement('div');
    header.className = 'uploaded-files-header';
    header.innerHTML = `
        <i class="fas fa-paperclip"></i>
        <span>الملفات المرفوعة (${uploadedChatFiles.length})</span>
    `;

    const filesList = document.createElement('div');
    filesList.className = 'uploaded-files-list';

    uploadedChatFiles.forEach(fileData => {
        const fileItem = createFileItem(fileData);
        filesList.appendChild(fileItem);
    });

    filesContainer.appendChild(header);
    filesContainer.appendChild(filesList);

    // إضافة أزرار الأوامر السريعة
    const quickCommands = createQuickCommandsSection();
    filesContainer.appendChild(quickCommands);

    // إضافة الحاوية إلى نافذة المحادثة
    chatWindow.appendChild(filesContainer);

    // التمرير إلى أسفل
    chatWindow.scrollTop = chatWindow.scrollHeight;
}

// إنشاء عنصر ملف
function createFileItem(fileData) {
    const fileItem = document.createElement('div');
    fileItem.className = 'uploaded-file-item';
    fileItem.dataset.fileId = fileData.id;

    // تحديد نوع الأيقونة
    let iconClass = 'fas fa-file';
    let iconType = 'text';

    switch (fileData.type) {
        case 'pdf':
            iconClass = 'fas fa-file-pdf';
            iconType = 'pdf';
            break;
        case 'docx':
            iconClass = 'fas fa-file-word';
            iconType = 'docx';
            break;
        case 'pptx':
            iconClass = 'fas fa-file-powerpoint';
            iconType = 'pptx';
            break;
        case 'csv':
            iconClass = 'fas fa-file-csv';
            iconType = 'csv';
            break;
        case 'jpg':
        case 'jpeg':
        case 'png':
        case 'gif':
        case 'svg':
        case 'webp':
        case 'bmp':
            iconClass = 'fas fa-file-image';
            iconType = 'image';
            break;
        case 'js':
        case 'ts':
        case 'jsx':
        case 'tsx':
        case 'py':
        case 'java':
        case 'cpp':
        case 'c':
        case 'h':
        case 'cs':
        case 'php':
        case 'rb':
        case 'go':
        case 'rs':
        case 'swift':
        case 'kt':
            iconClass = 'fas fa-file-code';
            iconType = 'code';
            break;
        default:
            iconClass = 'fas fa-file-alt';
            iconType = 'text';
    }

    fileItem.innerHTML = `
        <div class="file-icon ${iconType}">
            <i class="${iconClass}"></i>
        </div>
        <div class="file-info">
            <div class="file-name" title="${fileData.name}">${fileData.name}</div>
            <div class="file-details">
                <span>${fileData.size}</span>
                <span>${fileData.type.toUpperCase()}</span>
                <span>${fileData.processed ? 'معالج' : 'معالجة...'}</span>
            </div>
            ${fileData.preview ? `<div class="file-preview">${fileData.preview}</div>` : ''}
        </div>
        <div class="file-actions">
            <button class="file-action-btn extract" onclick="extractFileContent('${fileData.id}')" title="استخراج المحتوى">
                <i class="fas fa-eye"></i>
            </button>
            <button class="file-action-btn summarize" onclick="summarizeFile('${fileData.id}')" title="تلخيص">
                <i class="fas fa-compress-alt"></i>
            </button>
            <button class="file-action-btn remove" onclick="removeUploadedFile('${fileData.id}')" title="حذف">
                <i class="fas fa-trash"></i>
            </button>
        </div>
    `;

    return fileItem;
}

// استخراج محتوى الملف
function extractFileContent(fileId) {
    const fileData = uploadedChatFiles.find(f => f.id === fileId);
    if (!fileData) return;

    let content = `📄 محتوى الملف: ${fileData.name}\n\n`;

    if (fileData.imageUrl) {
        // للصور، عرض الصورة
        content += `نوع الملف: صورة\nالحجم: ${fileData.size}\n\n`;
        addMessage(content, 'bot');

        // إضافة الصورة كرسالة منفصلة
        const chatWindow = document.getElementById('chat-window');
        const imageMessage = document.createElement('div');
        imageMessage.className = 'message bot';
        imageMessage.innerHTML = `
            <div class="message-content">
                <img src="${fileData.imageUrl}" alt="${fileData.name}" style="max-width: 100%; max-height: 400px; border-radius: 8px; margin-top: 8px;">
            </div>
        `;
        chatWindow.appendChild(imageMessage);
        chatWindow.scrollTop = chatWindow.scrollHeight;
    } else {
        content += fileData.content;
        addMessage(content, 'bot');
    }
}

// تلخيص الملف
function summarizeFile(fileId) {
    const fileData = uploadedChatFiles.find(f => f.id === fileId);
    if (!fileData) return;

    let summary = `📋 ملخص الملف: ${fileData.name}\n\n`;

    if (fileData.csvData) {
        // ملخص خاص بملفات CSV
        summary += `نوع الملف: CSV\n`;
        summary += `عدد الصفوف: ${fileData.csvData.length}\n`;
        summary += `الأعمدة: ${Object.keys(fileData.csvData[0] || {}).join(', ')}\n\n`;
        summary += `عينة من البيانات:\n${fileData.csvData.slice(0, 3).map(row => Object.values(row).join(' | ')).join('\n')}`;
    } else if (fileData.imageUrl) {
        summary += `نوع الملف: صورة\n`;
        summary += `الحجم: ${fileData.size}\n`;
        summary += `التنسيق: ${fileData.type.toUpperCase()}`;
    } else {
        // ملخص عام للملفات النصية
        const words = fileData.content.split(/\s+/).length;
        const lines = fileData.content.split('\n').length;
        summary += `نوع الملف: ${fileData.type.toUpperCase()}\n`;
        summary += `الحجم: ${fileData.size}\n`;
        summary += `عدد الكلمات: ${words}\n`;
        summary += `عدد الأسطر: ${lines}\n\n`;
        summary += `المحتوى (أول 300 حرف):\n${fileData.content.substring(0, 300)}${fileData.content.length > 300 ? '...' : ''}`;
    }

    addMessage(summary, 'bot');
}

// حذف ملف مرفوع
function removeUploadedFile(fileId) {
    const index = uploadedChatFiles.findIndex(f => f.id === fileId);
    if (index === -1) return;

    // حذف الملف من المصفوفة
    const removedFile = uploadedChatFiles.splice(index, 1)[0];

    // حذف عنصر الملف من الواجهة
    const fileElement = document.querySelector(`[data-file-id="${fileId}"]`);
    if (fileElement) {
        fileElement.remove();
    }

    // إذا لم تعد هناك ملفات، احذف الحاوية بالكامل
    if (uploadedChatFiles.length === 0) {
        const container = document.querySelector('.uploaded-files-container');
        if (container) {
            container.remove();
        }
    } else {
        // تحديث عدد الملفات في العنوان
        const header = document.querySelector('.uploaded-files-header span');
        if (header) {
            header.textContent = `الملفات المرفوعة (${uploadedChatFiles.length})`;
        }
    }

    addMessage(`تم حذف الملف: ${removedFile.name}`, 'bot');
}

// نظام معالجة الأوامر المتقدم للملفات
class FileCommandProcessor {
    constructor() {
        this.commands = {
            // أوامر التلخيص
            summarize: {
                patterns: [
                    /لخص|تلخيص|ملخص|اختصر|summarize|summary/i,
                    /اعطني ملخص|قدم ملخص|اريد ملخص/i
                ],
                action: 'summarize'
            },

            // أوامر الشرح
            explain: {
                patterns: [
                    /اشرح|شرح|وضح|explain|clarify/i,
                    /ما معنى|ماذا يعني|كيف يعمل/i,
                    /اعطني شرح|قدم شرح|اريد شرح/i
                ],
                action: 'explain'
            },

            // أوامر التحليل
            analyze: {
                patterns: [
                    /حلل|تحليل|analyze|analysis/i,
                    /ما هي النتائج|ما هي الاحصائيات/i,
                    /اعطني تحليل|قدم تحليل|اريد تحليل/i
                ],
                action: 'analyze'
            },

            // أوامر الترجمة
            translate: {
                patterns: [
                    /ترجم|translate|translation/i,
                    /الى العربية|الى الانجليزية|to english|to arabic/i
                ],
                action: 'translate'
            },

            // أوامر الاستخراج
            extract: {
                patterns: [
                    /استخرج|extract|استخراج/i,
                    /اعطني|احصل على|get me|find/i,
                    /البحث عن|ابحث عن|search for/i
                ],
                action: 'extract'
            },

            // أوامر التحويل
            convert: {
                patterns: [
                    /حول|تحويل|convert|transformation/i,
                    /الى جدول|الى قائمة|to table|to list/i,
                    /الى json|الى csv|to json|to csv/i
                ],
                action: 'convert'
            },

            // أوامر المقارنة
            compare: {
                patterns: [
                    /قارن|مقارنة|compare|comparison/i,
                    /ما الفرق|الاختلافات|differences/i
                ],
                action: 'compare'
            },

            // أوامر الإحصائيات
            statistics: {
                patterns: [
                    /احصائيات|statistics|stats/i,
                    /كم عدد|how many|count/i,
                    /النسبة|المعدل|percentage|average/i
                ],
                action: 'statistics'
            }
        };
    }

    // تحليل الرسالة لاستخراج الأوامر
    parseMessage(message) {
        const detectedCommands = [];
        const lowerMessage = message.toLowerCase();

        for (const [commandName, commandData] of Object.entries(this.commands)) {
            for (const pattern of commandData.patterns) {
                if (pattern.test(lowerMessage)) {
                    detectedCommands.push({
                        command: commandName,
                        action: commandData.action,
                        originalText: message
                    });
                    break;
                }
            }
        }

        return detectedCommands;
    }

    // تنفيذ الأوامر على الملفات
    async executeCommands(commands, files, originalMessage) {
        if (!files || files.length === 0) {
            return "لا توجد ملفات مرفوعة لتنفيذ الأوامر عليها.";
        }

        let results = [];

        for (const command of commands) {
            for (const file of files) {
                try {
                    const result = await this.executeCommand(command, file, originalMessage);
                    results.push({
                        command: command.command,
                        file: file.name,
                        result: result
                    });
                } catch (error) {
                    results.push({
                        command: command.command,
                        file: file.name,
                        result: `خطأ في تنفيذ الأمر: ${error.message}`
                    });
                }
            }
        }

        return this.formatResults(results);
    }

    // تنفيذ أمر واحد على ملف واحد
    async executeCommand(command, file, originalMessage) {
        switch (command.action) {
            case 'summarize':
                return await this.summarizeFile(file, originalMessage);
            case 'explain':
                return await this.explainFile(file, originalMessage);
            case 'analyze':
                return await this.analyzeFile(file, originalMessage);
            case 'translate':
                return await this.translateFile(file, originalMessage);
            case 'extract':
                return await this.extractFromFile(file, originalMessage);
            case 'convert':
                return await this.convertFile(file, originalMessage);
            case 'compare':
                return await this.compareFiles(file, originalMessage);
            case 'statistics':
                return await this.getFileStatistics(file, originalMessage);
            default:
                return `أمر غير مدعوم: ${command.command}`;
        }
    }

    // تلخيص الملف
    async summarizeFile(file, originalMessage) {
        let summary = `📋 **ملخص الملف: ${file.name}**\n\n`;

        if (file.type === 'csv' && file.csvData) {
            summary += `**نوع الملف:** جدول بيانات CSV\n`;
            summary += `**عدد الصفوف:** ${file.csvData.length}\n`;
            summary += `**الأعمدة:** ${Object.keys(file.csvData[0] || {}).join(', ')}\n\n`;

            // تحليل البيانات الرقمية
            const numericColumns = this.getNumericColumns(file.csvData);
            if (numericColumns.length > 0) {
                summary += `**الأعمدة الرقمية:** ${numericColumns.join(', ')}\n`;
                for (const col of numericColumns) {
                    const stats = this.calculateColumnStats(file.csvData, col);
                    summary += `- ${col}: المتوسط ${stats.average.toFixed(2)}, الحد الأدنى ${stats.min}, الحد الأقصى ${stats.max}\n`;
                }
            }

            summary += `\n**عينة من البيانات:**\n`;
            summary += file.csvData.slice(0, 3).map(row =>
                Object.values(row).join(' | ')
            ).join('\n');

        } else if (file.imageUrl) {
            summary += `**نوع الملف:** صورة\n`;
            summary += `**التنسيق:** ${file.type.toUpperCase()}\n`;
            summary += `**الحجم:** ${file.size}\n`;
            summary += `**الوصف:** صورة بتنسيق ${file.type} بحجم ${file.size}`;

        } else if (file.content) {
            const words = file.content.split(/\s+/).length;
            const lines = file.content.split('\n').length;
            const chars = file.content.length;

            summary += `**نوع الملف:** ${file.type.toUpperCase()}\n`;
            summary += `**الإحصائيات:**\n`;
            summary += `- عدد الكلمات: ${words}\n`;
            summary += `- عدد الأسطر: ${lines}\n`;
            summary += `- عدد الأحرف: ${chars}\n\n`;

            // استخراج الكلمات المفتاحية
            const keywords = this.extractKeywords(file.content);
            if (keywords.length > 0) {
                summary += `**الكلمات المفتاحية:** ${keywords.slice(0, 10).join(', ')}\n\n`;
            }

            // ملخص المحتوى
            summary += `**ملخص المحتوى:**\n`;
            summary += this.generateContentSummary(file.content);
        }

        return summary;
    }

    // شرح الملف
    async explainFile(file, originalMessage) {
        let explanation = `📖 **شرح الملف: ${file.name}**\n\n`;

        if (file.type === 'csv' && file.csvData) {
            explanation += `هذا ملف CSV (Comma-Separated Values) يحتوي على بيانات جدولية منظمة.\n\n`;
            explanation += `**هيكل البيانات:**\n`;
            explanation += `- يحتوي على ${file.csvData.length} صف من البيانات\n`;
            explanation += `- يحتوي على ${Object.keys(file.csvData[0] || {}).length} عمود\n\n`;

            explanation += `**الأعمدة الموجودة:**\n`;
            Object.keys(file.csvData[0] || {}).forEach((col, index) => {
                const sampleValues = file.csvData.slice(0, 3).map(row => row[col]).filter(val => val);
                explanation += `${index + 1}. **${col}**: ${sampleValues.join(', ')}...\n`;
            });

        } else if (file.type === 'json') {
            explanation += `هذا ملف JSON (JavaScript Object Notation) يحتوي على بيانات منظمة.\n\n`;
            try {
                const jsonData = JSON.parse(file.content);
                explanation += `**هيكل البيانات:**\n`;
                explanation += this.explainJsonStructure(jsonData);
            } catch (e) {
                explanation += `الملف يحتوي على بيانات JSON ولكن قد يحتوي على أخطاء في التنسيق.`;
            }

        } else if (file.type === 'md') {
            explanation += `هذا ملف Markdown يحتوي على نص منسق.\n\n`;
            explanation += `**عناصر Markdown الموجودة:**\n`;
            const mdElements = this.analyzeMarkdownElements(file.content);
            explanation += mdElements;

        } else if (['js', 'py', 'java', 'cpp', 'c', 'cs', 'php'].includes(file.type)) {
            explanation += `هذا ملف كود برمجي بلغة ${this.getLanguageName(file.type)}.\n\n`;
            explanation += `**تحليل الكود:**\n`;
            explanation += this.analyzeCode(file.content, file.type);

        } else if (file.imageUrl) {
            explanation += `هذه صورة بتنسيق ${file.type.toUpperCase()}.\n\n`;
            explanation += `**معلومات الصورة:**\n`;
            explanation += `- التنسيق: ${file.type.toUpperCase()}\n`;
            explanation += `- الحجم: ${file.size}\n`;
            explanation += `- يمكن عرضها في المتصفحات والتطبيقات التي تدعم هذا التنسيق\n`;

        } else {
            explanation += `هذا ملف نصي يحتوي على معلومات مكتوبة.\n\n`;
            explanation += `**تحليل المحتوى:**\n`;
            explanation += this.analyzeGeneralText(file.content);
        }

        return explanation;
    }

    // تحليل الملف
    async analyzeFile(file, originalMessage) {
        let analysis = `🔍 **تحليل الملف: ${file.name}**\n\n`;

        if (file.type === 'csv' && file.csvData) {
            analysis += this.performCSVAnalysis(file.csvData);
        } else if (file.content) {
            analysis += this.performTextAnalysis(file.content, file.type);
        } else if (file.imageUrl) {
            analysis += this.performImageAnalysis(file);
        }

        return analysis;
    }

    // ترجمة الملف
    async translateFile(file, originalMessage) {
        let translation = `🌐 **ترجمة الملف: ${file.name}**\n\n`;

        // تحديد اللغة المطلوبة من الرسالة
        const targetLang = this.detectTargetLanguage(originalMessage);

        if (file.content) {
            translation += `**اللغة المستهدفة:** ${targetLang}\n\n`;
            translation += `**المحتوى المترجم:**\n`;
            // هنا يمكن إضافة خدمة ترجمة حقيقية
            translation += `[سيتم إضافة خدمة الترجمة قريباً]\n\n`;
            translation += `**المحتوى الأصلي:**\n${file.content.substring(0, 500)}...`;
        } else {
            translation += `لا يمكن ترجمة هذا النوع من الملفات.`;
        }

        return translation;
    }

    // استخراج من الملف
    async extractFromFile(file, originalMessage) {
        let extraction = `🔍 **استخراج من الملف: ${file.name}**\n\n`;

        // تحديد ما يريد المستخدم استخراجه
        const extractionTarget = this.detectExtractionTarget(originalMessage);

        if (file.type === 'csv' && file.csvData) {
            extraction += this.extractFromCSV(file.csvData, extractionTarget);
        } else if (file.content) {
            extraction += this.extractFromText(file.content, extractionTarget);
        } else {
            extraction += `لا يمكن الاستخراج من هذا النوع من الملفات.`;
        }

        return extraction;
    }

    // تحويل الملف
    async convertFile(file, originalMessage) {
        let conversion = `🔄 **تحويل الملف: ${file.name}**\n\n`;

        const targetFormat = this.detectTargetFormat(originalMessage);

        if (file.type === 'csv' && file.csvData) {
            conversion += this.convertCSV(file.csvData, targetFormat);
        } else if (file.content) {
            conversion += this.convertText(file.content, targetFormat, file.type);
        } else {
            conversion += `لا يمكن تحويل هذا النوع من الملفات.`;
        }

        return conversion;
    }

    // مقارنة الملفات
    async compareFiles(file, originalMessage) {
        let comparison = `⚖️ **مقارنة الملف: ${file.name}**\n\n`;

        // للمقارنة نحتاج ملفين على الأقل
        if (uploadedChatFiles.length < 2) {
            comparison += `يجب رفع ملفين على الأقل للمقارنة.`;
        } else {
            comparison += this.performFileComparison(file, uploadedChatFiles);
        }

        return comparison;
    }

    // إحصائيات الملف
    async getFileStatistics(file, originalMessage) {
        let stats = `📊 **إحصائيات الملف: ${file.name}**\n\n`;

        if (file.type === 'csv' && file.csvData) {
            stats += this.getCSVStatistics(file.csvData);
        } else if (file.content) {
            stats += this.getTextStatistics(file.content);
        } else if (file.imageUrl) {
            stats += this.getImageStatistics(file);
        } else {
            stats += `لا توجد إحصائيات متاحة لهذا النوع من الملفات.`;
        }

        return stats;
    }

    // وظائف مساعدة للتحليل
    getNumericColumns(data) {
        if (!data || data.length === 0) return [];

        const columns = Object.keys(data[0]);
        return columns.filter(col => {
            const values = data.slice(0, 10).map(row => row[col]);
            return values.some(val => !isNaN(parseFloat(val)) && isFinite(val));
        });
    }

    calculateColumnStats(data, column) {
        const values = data.map(row => parseFloat(row[column])).filter(val => !isNaN(val));
        if (values.length === 0) return { average: 0, min: 0, max: 0 };

        return {
            average: values.reduce((a, b) => a + b, 0) / values.length,
            min: Math.min(...values),
            max: Math.max(...values),
            count: values.length
        };
    }

    extractKeywords(text) {
        // استخراج الكلمات المفتاحية البسيط
        const words = text.toLowerCase()
            .replace(/[^\w\s\u0600-\u06FF]/g, ' ')
            .split(/\s+/)
            .filter(word => word.length > 3);

        const frequency = {};
        words.forEach(word => {
            frequency[word] = (frequency[word] || 0) + 1;
        });

        return Object.entries(frequency)
            .sort((a, b) => b[1] - a[1])
            .slice(0, 20)
            .map(([word]) => word);
    }

    generateContentSummary(content) {
        // تلخيص بسيط للمحتوى
        const sentences = content.split(/[.!?]+/).filter(s => s.trim().length > 10);
        const summary = sentences.slice(0, 3).join('. ');
        return summary + (sentences.length > 3 ? '...' : '');
    }

    explainJsonStructure(data, depth = 0) {
        let explanation = '';
        const indent = '  '.repeat(depth);

        if (Array.isArray(data)) {
            explanation += `${indent}مصفوفة تحتوي على ${data.length} عنصر\n`;
            if (data.length > 0) {
                explanation += this.explainJsonStructure(data[0], depth + 1);
            }
        } else if (typeof data === 'object' && data !== null) {
            const keys = Object.keys(data);
            explanation += `${indent}كائن يحتوي على ${keys.length} خاصية:\n`;
            keys.slice(0, 5).forEach(key => {
                explanation += `${indent}- ${key}: ${typeof data[key]}\n`;
            });
            if (keys.length > 5) {
                explanation += `${indent}... و ${keys.length - 5} خاصية أخرى\n`;
            }
        }

        return explanation;
    }

    analyzeMarkdownElements(content) {
        let analysis = '';

        const headers = content.match(/^#+\s+.+$/gm) || [];
        const lists = content.match(/^[\s]*[-*+]\s+.+$/gm) || [];
        const codeBlocks = content.match(/```[\s\S]*?```/g) || [];
        const links = content.match(/\[([^\]]+)\]\(([^)]+)\)/g) || [];
        const images = content.match(/!\[([^\]]*)\]\(([^)]+)\)/g) || [];

        analysis += `- العناوين: ${headers.length}\n`;
        analysis += `- عناصر القوائم: ${lists.length}\n`;
        analysis += `- كتل الكود: ${codeBlocks.length}\n`;
        analysis += `- الروابط: ${links.length}\n`;
        analysis += `- الصور: ${images.length}\n`;

        return analysis;
    }

    getLanguageName(type) {
        const languages = {
            'js': 'JavaScript',
            'ts': 'TypeScript',
            'py': 'Python',
            'java': 'Java',
            'cpp': 'C++',
            'c': 'C',
            'cs': 'C#',
            'php': 'PHP',
            'rb': 'Ruby',
            'go': 'Go',
            'rs': 'Rust',
            'swift': 'Swift',
            'kt': 'Kotlin'
        };
        return languages[type] || type.toUpperCase();
    }

    analyzeCode(content, type) {
        let analysis = '';

        const lines = content.split('\n').length;
        const functions = this.countFunctions(content, type);
        const comments = this.countComments(content, type);

        analysis += `- عدد الأسطر: ${lines}\n`;
        analysis += `- عدد الوظائف: ${functions}\n`;
        analysis += `- عدد التعليقات: ${comments}\n`;

        return analysis;
    }

    countFunctions(content, type) {
        const patterns = {
            'js': /function\s+\w+|const\s+\w+\s*=\s*\(|=>\s*{/g,
            'py': /def\s+\w+/g,
            'java': /(public|private|protected)?\s*(static\s+)?\w+\s+\w+\s*\(/g,
            'cpp': /\w+\s+\w+\s*\([^)]*\)\s*{/g,
            'c': /\w+\s+\w+\s*\([^)]*\)\s*{/g
        };

        const pattern = patterns[type];
        return pattern ? (content.match(pattern) || []).length : 0;
    }

    countComments(content, type) {
        const patterns = {
            'js': /\/\/.*|\/\*[\s\S]*?\*\//g,
            'py': /#.*/g,
            'java': /\/\/.*|\/\*[\s\S]*?\*\//g,
            'cpp': /\/\/.*|\/\*[\s\S]*?\*\//g,
            'c': /\/\/.*|\/\*[\s\S]*?\*\//g
        };

        const pattern = patterns[type];
        return pattern ? (content.match(pattern) || []).length : 0;
    }

    // تنسيق النتائج
    formatResults(results) {
        if (results.length === 0) {
            return "لم يتم العثور على أوامر قابلة للتنفيذ.";
        }

        let formatted = "## 🎯 نتائج تنفيذ الأوامر\n\n";

        results.forEach((result, index) => {
            formatted += `### ${index + 1}. ${result.command.toUpperCase()} - ${result.file}\n\n`;
            formatted += result.result + "\n\n";
            formatted += "---\n\n";
        });

        return formatted;
    }

    // وظائف مساعدة إضافية
    detectTargetLanguage(message) {
        if (/الى العربية|to arabic/i.test(message)) return 'العربية';
        if (/الى الانجليزية|to english/i.test(message)) return 'الإنجليزية';
        return 'غير محدد';
    }

    detectExtractionTarget(message) {
        if (/ايميل|email/i.test(message)) return 'emails';
        if (/رقم|number|phone/i.test(message)) return 'numbers';
        if (/تاريخ|date/i.test(message)) return 'dates';
        if (/رابط|link|url/i.test(message)) return 'links';
        return 'general';
    }

    detectTargetFormat(message) {
        if (/json/i.test(message)) return 'json';
        if (/csv/i.test(message)) return 'csv';
        if (/جدول|table/i.test(message)) return 'table';
        if (/قائمة|list/i.test(message)) return 'list';
        return 'text';
    }

    extractFromCSV(data, target) {
        let result = `**استخراج ${target} من البيانات:**\n\n`;

        switch (target) {
            case 'numbers':
                const numericColumns = this.getNumericColumns(data);
                result += `الأعمدة الرقمية: ${numericColumns.join(', ')}\n`;
                numericColumns.forEach(col => {
                    const stats = this.calculateColumnStats(data, col);
                    result += `- ${col}: المجموع ${stats.count}, المتوسط ${stats.average.toFixed(2)}\n`;
                });
                break;
            case 'emails':
                result += this.extractEmails(JSON.stringify(data));
                break;
            default:
                result += `عرض أول 5 صفوف:\n`;
                result += data.slice(0, 5).map(row =>
                    Object.values(row).join(' | ')
                ).join('\n');
        }

        return result;
    }

    extractFromText(content, target) {
        let result = `**استخراج ${target} من النص:**\n\n`;

        switch (target) {
            case 'emails':
                result += this.extractEmails(content);
                break;
            case 'numbers':
                result += this.extractNumbers(content);
                break;
            case 'dates':
                result += this.extractDates(content);
                break;
            case 'links':
                result += this.extractLinks(content);
                break;
            default:
                result += content.substring(0, 500) + '...';
        }

        return result;
    }

    extractEmails(text) {
        const emailRegex = /[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}/g;
        const emails = text.match(emailRegex) || [];
        return emails.length > 0 ? emails.join('\n') : 'لم يتم العثور على عناوين بريد إلكتروني';
    }

    extractNumbers(text) {
        const numberRegex = /\b\d+(?:\.\d+)?\b/g;
        const numbers = text.match(numberRegex) || [];
        return numbers.length > 0 ? numbers.slice(0, 20).join(', ') : 'لم يتم العثور على أرقام';
    }

    extractDates(text) {
        const dateRegex = /\b\d{1,2}[\/\-\.]\d{1,2}[\/\-\.]\d{2,4}\b/g;
        const dates = text.match(dateRegex) || [];
        return dates.length > 0 ? dates.join('\n') : 'لم يتم العثور على تواريخ';
    }

    extractLinks(text) {
        const linkRegex = /https?:\/\/[^\s]+/g;
        const links = text.match(linkRegex) || [];
        return links.length > 0 ? links.join('\n') : 'لم يتم العثور على روابط';
    }

    convertCSV(data, format) {
        let result = `**تحويل البيانات إلى ${format}:**\n\n`;

        switch (format) {
            case 'json':
                result += '```json\n' + JSON.stringify(data.slice(0, 5), null, 2) + '\n```';
                break;
            case 'table':
                result += this.formatAsTable(data.slice(0, 10));
                break;
            case 'list':
                result += this.formatAsList(data.slice(0, 10));
                break;
            default:
                result += 'تنسيق غير مدعوم';
        }

        return result;
    }

    convertText(content, format, originalType) {
        let result = `**تحويل النص من ${originalType} إلى ${format}:**\n\n`;

        switch (format) {
            case 'json':
                try {
                    const lines = content.split('\n').filter(line => line.trim());
                    const jsonData = { lines: lines.slice(0, 10) };
                    result += '```json\n' + JSON.stringify(jsonData, null, 2) + '\n```';
                } catch (e) {
                    result += 'خطأ في التحويل إلى JSON';
                }
                break;
            case 'list':
                const lines = content.split('\n').filter(line => line.trim());
                result += lines.slice(0, 10).map((line, i) => `${i + 1}. ${line}`).join('\n');
                break;
            default:
                result += 'تنسيق غير مدعوم';
        }

        return result;
    }

    formatAsTable(data) {
        if (!data || data.length === 0) return 'لا توجد بيانات';

        const headers = Object.keys(data[0]);
        let table = '| ' + headers.join(' | ') + ' |\n';
        table += '|' + headers.map(() => '---').join('|') + '|\n';

        data.forEach(row => {
            table += '| ' + headers.map(h => row[h] || '').join(' | ') + ' |\n';
        });

        return table;
    }

    formatAsList(data) {
        if (!data || data.length === 0) return 'لا توجد بيانات';

        return data.map((row, i) => {
            const items = Object.entries(row).map(([key, value]) => `${key}: ${value}`);
            return `${i + 1}. ${items.join(', ')}`;
        }).join('\n');
    }

    performFileComparison(file, allFiles) {
        let comparison = `مقارنة "${file.name}" مع الملفات الأخرى:\n\n`;

        const otherFiles = allFiles.filter(f => f.id !== file.id);

        otherFiles.forEach(otherFile => {
            comparison += `**مقارنة مع "${otherFile.name}":**\n`;
            comparison += `- النوع: ${file.type} vs ${otherFile.type}\n`;
            comparison += `- الحجم: ${file.size} vs ${otherFile.size}\n`;

            if (file.content && otherFile.content) {
                const similarity = this.calculateSimilarity(file.content, otherFile.content);
                comparison += `- التشابه: ${similarity.toFixed(2)}%\n`;
            }

            comparison += '\n';
        });

        return comparison;
    }

    calculateSimilarity(text1, text2) {
        const words1 = text1.toLowerCase().split(/\s+/);
        const words2 = text2.toLowerCase().split(/\s+/);

        const commonWords = words1.filter(word => words2.includes(word));
        const totalWords = new Set([...words1, ...words2]).size;

        return (commonWords.length / totalWords) * 100;
    }

    getCSVStatistics(data) {
        let stats = `**إحصائيات CSV:**\n\n`;
        stats += `- عدد الصفوف: ${data.length}\n`;
        stats += `- عدد الأعمدة: ${Object.keys(data[0] || {}).length}\n\n`;

        const numericColumns = this.getNumericColumns(data);
        if (numericColumns.length > 0) {
            stats += `**الإحصائيات الرقمية:**\n`;
            numericColumns.forEach(col => {
                const colStats = this.calculateColumnStats(data, col);
                stats += `- ${col}:\n`;
                stats += `  - المتوسط: ${colStats.average.toFixed(2)}\n`;
                stats += `  - الحد الأدنى: ${colStats.min}\n`;
                stats += `  - الحد الأقصى: ${colStats.max}\n`;
                stats += `  - العدد: ${colStats.count}\n\n`;
            });
        }

        return stats;
    }

    getTextStatistics(content) {
        const words = content.split(/\s+/).length;
        const chars = content.length;
        const lines = content.split('\n').length;
        const paragraphs = content.split(/\n\s*\n/).length;

        let stats = `**إحصائيات النص:**\n\n`;
        stats += `- عدد الكلمات: ${words}\n`;
        stats += `- عدد الأحرف: ${chars}\n`;
        stats += `- عدد الأسطر: ${lines}\n`;
        stats += `- عدد الفقرات: ${paragraphs}\n\n`;

        // تحليل اللغة
        const arabicChars = (content.match(/[\u0600-\u06FF]/g) || []).length;
        const englishChars = (content.match(/[a-zA-Z]/g) || []).length;

        stats += `**تحليل اللغة:**\n`;
        stats += `- أحرف عربية: ${arabicChars}\n`;
        stats += `- أحرف إنجليزية: ${englishChars}\n`;

        return stats;
    }

    getImageStatistics(file) {
        let stats = `**إحصائيات الصورة:**\n\n`;
        stats += `- الاسم: ${file.name}\n`;
        stats += `- النوع: ${file.type.toUpperCase()}\n`;
        stats += `- الحجم: ${file.size}\n`;
        stats += `- التنسيق: ${file.type}\n`;

        return stats;
    }

    performCSVAnalysis(data) {
        let analysis = `**تحليل شامل للبيانات:**\n\n`;

        // تحليل عام
        analysis += `- إجمالي الصفوف: ${data.length}\n`;
        analysis += `- إجمالي الأعمدة: ${Object.keys(data[0] || {}).length}\n\n`;

        // تحليل الأعمدة
        const columns = Object.keys(data[0] || {});
        analysis += `**تحليل الأعمدة:**\n`;

        columns.forEach(col => {
            const values = data.map(row => row[col]).filter(val => val !== null && val !== undefined && val !== '');
            const uniqueValues = new Set(values);

            analysis += `- ${col}:\n`;
            analysis += `  - القيم المملوءة: ${values.length}/${data.length}\n`;
            analysis += `  - القيم الفريدة: ${uniqueValues.size}\n`;

            // تحقق من كون العمود رقمي
            const numericValues = values.filter(val => !isNaN(parseFloat(val)) && isFinite(val));
            if (numericValues.length > values.length * 0.8) {
                const stats = this.calculateColumnStats(data, col);
                analysis += `  - نوع البيانات: رقمي\n`;
                analysis += `  - المتوسط: ${stats.average.toFixed(2)}\n`;
                analysis += `  - المدى: ${stats.min} - ${stats.max}\n`;
            } else {
                analysis += `  - نوع البيانات: نصي\n`;
                analysis += `  - أكثر القيم تكراراً: ${this.getMostFrequent(values)}\n`;
            }
            analysis += '\n';
        });

        return analysis;
    }

    getMostFrequent(values) {
        const frequency = {};
        values.forEach(val => {
            frequency[val] = (frequency[val] || 0) + 1;
        });

        const sorted = Object.entries(frequency).sort((a, b) => b[1] - a[1]);
        return sorted.length > 0 ? sorted[0][0] : 'غير محدد';
    }

    performTextAnalysis(content, type) {
        let analysis = `**تحليل النص:**\n\n`;

        // إحصائيات أساسية
        const words = content.split(/\s+/);
        const sentences = content.split(/[.!?]+/).filter(s => s.trim().length > 0);

        analysis += `- عدد الكلمات: ${words.length}\n`;
        analysis += `- عدد الجمل: ${sentences.length}\n`;
        analysis += `- متوسط الكلمات في الجملة: ${(words.length / sentences.length).toFixed(2)}\n\n`;

        // تحليل المحتوى حسب النوع
        if (type === 'md') {
            analysis += this.analyzeMarkdownContent(content);
        } else if (['js', 'py', 'java'].includes(type)) {
            analysis += this.analyzeCodeContent(content, type);
        } else {
            analysis += this.analyzeGeneralText(content);
        }

        return analysis;
    }

    analyzeMarkdownContent(content) {
        const headers = content.match(/^#+\s+.+$/gm) || [];
        const codeBlocks = content.match(/```[\s\S]*?```/g) || [];
        const links = content.match(/\[([^\]]+)\]\(([^)]+)\)/g) || [];

        let analysis = `**تحليل Markdown:**\n`;
        analysis += `- العناوين: ${headers.length}\n`;
        analysis += `- كتل الكود: ${codeBlocks.length}\n`;
        analysis += `- الروابط: ${links.length}\n`;

        return analysis;
    }

    analyzeCodeContent(content, type) {
        let analysis = `**تحليل الكود (${this.getLanguageName(type)}):**\n`;

        const functions = this.countFunctions(content, type);
        const comments = this.countComments(content, type);
        const lines = content.split('\n').length;
        const emptyLines = content.split('\n').filter(line => line.trim() === '').length;

        analysis += `- عدد الوظائف: ${functions}\n`;
        analysis += `- عدد التعليقات: ${comments}\n`;
        analysis += `- الأسطر الفارغة: ${emptyLines}\n`;
        analysis += `- كثافة التعليقات: ${((comments / lines) * 100).toFixed(2)}%\n`;

        return analysis;
    }

    analyzeGeneralText(content) {
        let analysis = `**تحليل النص العام:**\n`;

        // تحليل اللغة
        const arabicWords = (content.match(/[\u0600-\u06FF]+/g) || []).length;
        const englishWords = (content.match(/[a-zA-Z]+/g) || []).length;

        analysis += `- الكلمات العربية: ${arabicWords}\n`;
        analysis += `- الكلمات الإنجليزية: ${englishWords}\n`;

        // الكلمات الأكثر تكراراً
        const keywords = this.extractKeywords(content);
        if (keywords.length > 0) {
            analysis += `- أهم الكلمات: ${keywords.slice(0, 5).join(', ')}\n`;
        }

        return analysis;
    }

    performImageAnalysis(file) {
        let analysis = `**تحليل الصورة:**\n\n`;
        analysis += `- النوع: ${file.type.toUpperCase()}\n`;
        analysis += `- الحجم: ${file.size}\n`;
        analysis += `- التنسيق: صورة رقمية\n`;
        analysis += `- يمكن عرضها في المتصفح: نعم\n`;

        return analysis;
    }
}

// إنشاء مثيل من معالج الأوامر
const fileCommandProcessor = new FileCommandProcessor();

// إنشاء قسم الأوامر السريعة
function createQuickCommandsSection() {
    const quickCommands = document.createElement('div');
    quickCommands.className = 'quick-commands';

    const header = document.createElement('div');
    header.className = 'quick-commands-header';
    header.innerHTML = `
        <i class="fas fa-bolt"></i>
        <span>أوامر سريعة</span>
    `;

    const commandsList = document.createElement('div');
    commandsList.className = 'quick-commands-list';

    const commands = [
        { text: 'لخص الملفات', icon: '📋', command: 'لخص' },
        { text: 'اشرح المحتوى', icon: '📖', command: 'اشرح' },
        { text: 'حلل البيانات', icon: '🔍', command: 'حلل' },
        { text: 'استخرج الأرقام', icon: '🔢', command: 'استخرج الأرقام' },
        { text: 'استخرج الإيميلات', icon: '📧', command: 'استخرج الإيميلات' },
        { text: 'حول إلى JSON', icon: '🔄', command: 'حول إلى json' },
        { text: 'إحصائيات', icon: '📊', command: 'احصائيات' },
        { text: 'قارن الملفات', icon: '⚖️', command: 'قارن' }
    ];

    commands.forEach(cmd => {
        const button = document.createElement('button');
        button.className = 'quick-command-btn';
        button.innerHTML = `${cmd.icon} ${cmd.text}`;
        button.onclick = () => executeQuickCommand(cmd.command);
        commandsList.appendChild(button);
    });

    quickCommands.appendChild(header);
    quickCommands.appendChild(commandsList);

    return quickCommands;
}

// تنفيذ الأوامر السريعة
async function executeQuickCommand(command) {
    if (uploadedChatFiles.length === 0) {
        addMessage('لا توجد ملفات مرفوعة لتنفيذ الأمر عليها.', 'bot', true);
        return;
    }

    // إضافة رسالة المستخدم
    addMessage(command, 'user');

    // إظهار مؤشر التحميل
    const chatWindow = document.getElementById('chat-window');
    const typingDiv = document.createElement('div');
    typingDiv.className = 'message bot typing-indicator';
    typingDiv.innerHTML = `<div class="typing-dot"></div><div class="typing-dot"></div><div class="typing-dot"></div>`;
    chatWindow.appendChild(typingDiv);
    typingDiv.scrollIntoView({ behavior: 'smooth' });

    try {
        // تحليل الأمر وتنفيذه
        const detectedCommands = fileCommandProcessor.parseMessage(command);

        if (detectedCommands.length > 0) {
            const result = await fileCommandProcessor.executeCommands(
                detectedCommands,
                uploadedChatFiles,
                command
            );

            typingDiv.remove();
            addMessage(result, 'bot');

            // تمييز الحاوية لإظهار أن الأوامر تم تنفيذها
            const filesContainer = document.querySelector('.uploaded-files-container');
            if (filesContainer) {
                filesContainer.classList.add('has-commands');
            }
        } else {
            typingDiv.remove();
            addMessage('لم يتم التعرف على الأمر. جرب أحد الأوامر المتاحة.', 'bot', true);
        }
    } catch (error) {
        typingDiv.remove();
        console.error('خطأ في تنفيذ الأمر السريع:', error);
        addMessage('حدث خطأ أثناء تنفيذ الأمر. يرجى المحاولة مرة أخرى.', 'bot', true);
    }
}

// تحديث وظيفة عرض النتائج مع إضافة نتائج الأوامر
function displayCommandResult(command, result, fileId) {
    const fileItem = document.querySelector(`[data-file-id="${fileId}"]`);
    if (!fileItem) return;

    // إزالة النتائج السابقة
    const existingResult = fileItem.querySelector('.command-result');
    if (existingResult) {
        existingResult.remove();
    }

    // إنشاء عنصر النتيجة
    const resultDiv = document.createElement('div');
    resultDiv.className = 'command-result';

    const resultHeader = document.createElement('div');
    resultHeader.className = 'command-result-header';
    resultHeader.innerHTML = `
        <i class="fas fa-check-circle"></i>
        <span>نتيجة: ${command}</span>
    `;

    const resultContent = document.createElement('div');
    resultContent.className = 'command-result-content';
    resultContent.textContent = result;

    resultDiv.appendChild(resultHeader);
    resultDiv.appendChild(resultContent);

    // إضافة النتيجة إلى عنصر الملف
    fileItem.appendChild(resultDiv);
}

// وظائف لواجهة تشغيل الأكواد
function showCodeExecutor(codeBlock, suggestedName, lang, content) {
    const executor = document.getElementById('code-executor');
    const editorContainer = document.getElementById('editor-container');

    // Create file if it doesn't exist
    const fileId = createFile(suggestedName, content.trim(), lang || 'javascript');
    activeFileId = fileId;

    // إضافة الملف إلى قائمة الملفات المفتوحة
    const openFiles = JSON.parse(localStorage.getItem('openFiles') || '[]');
    if (!openFiles.includes(fileId)) {
        openFiles.push(fileId);
        localStorage.setItem('openFiles', JSON.stringify(openFiles));
    }

    if (typeof monaco === 'undefined') {
        console.log('Monaco Editor not loaded yet. Waiting...');
        editorContainer.innerHTML = '<div style="padding: 20px; color: #fff;">Loading editor...</div>';

        setTimeout(() => {
            showCodeExecutor(codeBlock, suggestedName, lang, content);
        }, 1000);
        return;
    }

    openFile(fileId);

    // Ensure terminal element exists
    let terminalContainer = document.querySelector('.executor-footer');
    if (!terminalContainer) {
        console.warn('Terminal container not found, creating it');
        terminalContainer = document.createElement('div');
        terminalContainer.className = 'executor-footer';
        executor.appendChild(terminalContainer);

        // Create terminal header
        const terminalHeader = document.createElement('div');
        terminalHeader.className = 'terminal-header';
        terminalHeader.innerHTML = `
            <span>النتائج</span>
            <button onclick="clearExecutorResult()">مسح</button>
        `;
        terminalContainer.appendChild(terminalHeader);

        // Create terminal output area
        const terminalOutput = document.createElement('div');
        terminalOutput.className = 'terminal';
        terminalOutput.id = 'executor-result';
        terminalContainer.appendChild(terminalOutput);
    }

    updateTerminalHeader();
    addStatusBar();

    // Update language indicator in status bar
    if (lang) {
        const langIndicator = document.querySelector('.language-indicator .status-item-text');
        if (langIndicator) {
            langIndicator.textContent = lang.charAt(0).toUpperCase() + lang.slice(1);
        }
    }

    executor.classList.add('visible');
}

// تحسين وظائف التيرمنال
function updateTerminalHeader() {
    const terminalHeader = document.querySelector('.terminal-header');
    if (terminalHeader) {
        terminalHeader.innerHTML = `
                    <div class="terminal-tabs">
                <div class="terminal-tab active" data-tab="terminal">TERMINAL</div>
                <div class="terminal-tab" data-tab="output">OUTPUT</div>
                <div class="terminal-tab" data-tab="problems">PROBLEMS</div>
                <div class="terminal-tab" data-tab="debug">DEBUG CONSOLE</div>
                    </div>
                    <div class="terminal-actions">
                        <button title="Clear Terminal" onclick="clearExecutorResult()"><i class="fas fa-trash-alt"></i></button>
                <button title="Kill Terminal" onclick="killTerminal()"><i class="fas fa-times-circle"></i></button>
                        <button title="New Terminal" onclick="createNewTerminal()"><i class="fas fa-plus"></i></button>
                        <button title="Split Terminal" onclick="splitTerminal()"><i class="fas fa-columns"></i></button>
                        <button title="Toggle Terminal" onclick="toggleTerminal()"><i class="fas fa-chevron-down"></i></button>
                <button title="Maximize Terminal" onclick="maximizeTerminal()"><i class="fas fa-expand-alt"></i></button>
                <button title="More Options" onclick="showTerminalOptions(event)"><i class="fas fa-ellipsis-v"></i></button>
                <button title="Close Terminal" onclick="hideTerminal()"><i class="fas fa-times"></i></button>
                    </div>
                `;

        // جعل التبويبات قابلة للنقر
        const tabs = terminalHeader.querySelectorAll('.terminal-tab');
        tabs.forEach(tab => {
            tab.addEventListener('click', function() {
                tabs.forEach(t => t.classList.remove('active'));
                this.classList.add('active');

                // تحديث محتوى التيرمنال بناءً على التبويب النشط
                const tabName = this.dataset.tab;
                const terminalElem = document.getElementById('executor-result');

                switch(tabName) {
                    case 'problems':
                        terminalElem.innerHTML = '<div class="terminal-message success">No problems have been detected in the workspace.</div>';
                        break;
                    case 'output':
                        terminalElem.innerHTML = '<div class="terminal-message">Output channel is empty.</div>';
                        break;
                    case 'debug':
                        terminalElem.innerHTML = '<div class="terminal-message">Debug console is available in debug mode.</div>';
                        break;
                    case 'terminal':
                    default:
                        // استعادة محتوى التيرمنال الأصلي إذا كان موجوداً
                        if (window._terminalContent) {
                            terminalElem.innerHTML = window._terminalContent;
                        } else {
                            terminalElem.innerHTML = '<div class="terminal-welcome">Terminal ready.</div>';
                        }
                }
            });
        });
    }
}

// حفظ محتوى التيرمنال قبل تغيير التبويب
function saveTerminalContent() {
    const terminalElem = document.getElementById('executor-result');
    if (terminalElem && terminalElem.innerHTML.trim() !== '') {
        window._terminalContent = terminalElem.innerHTML;
    }
}

// إخفاء/إظهار التيرمنال بشكل كامل
function toggleTerminal() {
    const executorFooter = document.querySelector('.executor-footer');
    if (executorFooter) {
        if (executorFooter.classList.contains('collapsed')) {
            // إذا كان مطوياً، نفتحه
            executorFooter.classList.remove('collapsed');
            localStorage.setItem('terminalState', 'open');
        } else if (executorFooter.classList.contains('hidden')) {
            // إذا كان مخفياً، نظهره
            executorFooter.classList.remove('hidden');
            localStorage.setItem('terminalState', 'open');
        } else {
            // إذا كان مفتوحاً، نطويه
            executorFooter.classList.add('collapsed');
            localStorage.setItem('terminalState', 'collapsed');
        }
    }
}

// إخفاء التيرمنال تماماً
function hideTerminal() {
    const executorFooter = document.querySelector('.executor-footer');
    if (executorFooter) {
        executorFooter.classList.add('hidden');
        localStorage.setItem('terminalState', 'hidden');
    }
}

// تكبير التيرمنال
function maximizeTerminal() {
    const executorFooter = document.querySelector('.executor-footer');
    if (executorFooter) {
        executorFooter.classList.toggle('maximized');
        if (executorFooter.classList.contains('maximized')) {
            localStorage.setItem('terminalState', 'maximized');
        } else {
            localStorage.setItem('terminalState', 'open');
        }
    }
}

// إيقاف عمليات التيرمنال
function killTerminal() {
    const terminalElem = document.getElementById('executor-result');
    if (terminalElem) {
        saveTerminalContent();
        terminalElem.innerHTML += '<div class="terminal-message error">Process terminated.</div>';
    }
}

// استعادة حالة التيرمنال عند تحميل الصفحة
function restoreTerminalState() {
    const state = localStorage.getItem('terminalState') || 'open';
    const executorFooter = document.querySelector('.executor-footer');

    if (executorFooter) {
        // إزالة جميع الحالات أولاً
        executorFooter.classList.remove('collapsed', 'hidden', 'maximized');
        executorFooter.style.height = '';

        // تطبيق الحالة المحفوظة
        switch (state) {
            case 'collapsed':
                executorFooter.classList.add('collapsed');
                break;
            case 'hidden':
                executorFooter.classList.add('hidden');
                break;
            case 'maximized':
                executorFooter.classList.add('maximized');
                break;
            case 'custom':
                const savedHeight = localStorage.getItem('terminalHeight');
                if (savedHeight) {
                    executorFooter.style.height = savedHeight + 'px';
                }
                break;
        }
    }
}

// إضافة زر لفتح/إغلاق التيرمنال في شريط الحالة
function addTerminalToggleButton() {
    // تحقق مما إذا كان شريط الحالة موجوداً
    const statusBar = document.querySelector('.status-bar');
    if (!statusBar) return;

    // تحقق مما إذا كان الزر موجوداً بالفعل
    if (statusBar.querySelector('.terminal-toggle-btn')) return;

    // إنشاء زر التبديل
    const toggleBtn = document.createElement('div');
    toggleBtn.className = 'status-item terminal-toggle-btn';
    toggleBtn.innerHTML = '<i class="fas fa-terminal"></i> Terminal';
    toggleBtn.title = 'Toggle Terminal (Ctrl+`)';
    toggleBtn.onclick = function() {
        const executorFooter = document.querySelector('.executor-footer');
        if (executorFooter) {
            if (executorFooter.classList.contains('hidden')) {
                executorFooter.classList.remove('hidden');
                localStorage.setItem('terminalState', 'open');
            } else if (executorFooter.classList.contains('collapsed')) {
                executorFooter.classList.remove('collapsed');
                localStorage.setItem('terminalState', 'open');
            } else {
                executorFooter.classList.add('hidden');
                localStorage.setItem('terminalState', 'hidden');
            }
        }
    };

    // إضافة الزر إلى شريط الحالة
    const leftItems = statusBar.querySelector('.status-items-left');
    if (leftItems) {
        leftItems.appendChild(toggleBtn);
    } else {
        statusBar.appendChild(toggleBtn);
    }
}

// إضافة مقبض تغيير حجم التيرمنال
function addTerminalResizer() {
    const executorFooter = document.querySelector('.executor-footer');
    if (!executorFooter) return;

    // تحقق مما إذا كان المقبض موجوداً بالفعل
    if (executorFooter.querySelector('.terminal-resizer')) return;

    // إنشاء مقبض التغيير
    const resizer = document.createElement('div');
    resizer.className = 'terminal-resizer';
    executorFooter.appendChild(resizer);

    // تفعيل وظيفة السحب
    let startY, startHeight;

    resizer.addEventListener('mousedown', function(e) {
        startY = e.clientY;
        startHeight = parseInt(getComputedStyle(executorFooter).height);
        document.addEventListener('mousemove', doDrag, false);
        document.addEventListener('mouseup', stopDrag, false);
        document.body.style.cursor = 'row-resize';
        e.preventDefault();
    });

    function doDrag(e) {
        // حساب الارتفاع الجديد (السحب لأعلى يقلل الارتفاع)
        const newHeight = startHeight - (e.clientY - startY);
        // تحديد حد أدنى وأقصى للارتفاع
        const minHeight = 100; // الحد الأدنى للارتفاع
        const maxHeight = window.innerHeight * 0.8; // 80% من ارتفاع النافذة

        if (newHeight > minHeight && newHeight < maxHeight) {
            executorFooter.style.height = newHeight + 'px';
            executorFooter.classList.remove('collapsed', 'maximized');
            localStorage.setItem('terminalHeight', newHeight);
            localStorage.setItem('terminalState', 'custom');
        }
    }

    function stopDrag() {
        document.removeEventListener('mousemove', doDrag, false);
        document.removeEventListener('mouseup', stopDrag, false);
        document.body.style.cursor = '';
    }
}





// تعديل دالة clearExecutorResult للحفاظ على تنسيق التيرمنال
function clearExecutorResult() {
    const resultElement = document.getElementById('executor-result');
    if (resultElement) {
        resultElement.innerHTML = '<div class="terminal-welcome">Terminal cleared.</div>';

    }
}

// إضافة اختصارات لوحة المفاتيح
document.addEventListener('keydown', function(e) {
    // Ctrl+S للحفظ
    if (e.ctrlKey && e.key === 's') {
        e.preventDefault();
        if (activeFileId && workspace.files[activeFileId]) {
            // حفظ الملف النشط
            if (monacoEditor) {
                workspace.files[activeFileId].content = monacoEditor.getValue();
            }
            saveWorkspace();

            // إظهار إشعار نجاح
            const fileName = workspace.files[activeFileId].name;
            showSuccess(`تم حفظ الملف "${fileName}" بنجاح!`, 'حفظ ملف');
        }
        return;
    }

    // Ctrl+` لفتح/إغلاق التيرمنال
    if (e.ctrlKey && (e.key === '`' || e.key === 'Backquote')) {
        const executorFooter = document.querySelector('.executor-footer');
        if (executorFooter) {
            if (executorFooter.classList.contains('hidden')) {
                executorFooter.classList.remove('hidden');
                localStorage.setItem('terminalState', 'open');
            } else {
        executorFooter.classList.toggle('collapsed');
                localStorage.setItem('terminalState',
                    executorFooter.classList.contains('collapsed') ? 'collapsed' : 'open');
    }
            e.preventDefault();
}
    }
});

function updateFileTabs() {
    const tabsContainer = document.getElementById('file-tabs');
    tabsContainer.innerHTML = '';

    // تعديل: عرض جميع الملفات المفتوحة بدلاً من الملف النشط فقط
    // نحتفظ بقائمة من الملفات المفتوحة
    const openFiles = [];

    // إذا كان هناك ملف نشط، نضيفه أولاً
    if (activeFileId && workspace.files[activeFileId]) {
        openFiles.push(activeFileId);
    }

    // نضيف باقي الملفات المفتوحة من localStorage
    const savedOpenFiles = JSON.parse(localStorage.getItem('openFiles') || '[]');
    savedOpenFiles.forEach(fileId => {
        if (workspace.files[fileId] && !openFiles.includes(fileId)) {
            openFiles.push(fileId);
        }
    });

    // عرض جميع الملفات المفتوحة كتبويبات
    openFiles.forEach(fileId => {
        const file = workspace.files[fileId];
        const tab = document.createElement('div');
        tab.className = `file-tab ${fileId === activeFileId ? 'active' : ''}`;

        // تحقق مما إذا كان المحرر مفتوحاً حاليًا
        const isEditorVisible = document.getElementById('code-executor').classList.contains('visible');

        tab.innerHTML = `
                    <span class="file-tab-name">${file.name}</span>
                    ${!isEditorVisible && fileId === activeFileId ?
                `<span class="file-tab-open" onclick="reopenEditor(event)" title="إعادة فتح المحرر">
                            <i class="fas fa-external-link-alt"></i>
                        </span>` : ''
            }
                    <span class="file-tab-close" onclick="closeFile('${file.id}', event)">×</span>
                `;
        tab.onclick = (e) => {
            if (!e.target.closest('.file-tab-close') && !e.target.closest('.file-tab-open')) {
                // تنشيط هذا الملف
                activeFileId = file.id;
                updateFileTabs();

                // إذا كان المحرر مغلقًا، نعيد فتحه
                if (!document.getElementById('code-executor').classList.contains('visible')) {
                    reopenEditor();
                } else {
                    // إذا كان المحرر مفتوحًا، نقوم بتحميل هذا الملف
                    openFile(file.id);
                }
            }
        };
        tabsContainer.appendChild(tab);
    });

    // حفظ الملفات المفتوحة في localStorage
    localStorage.setItem('openFiles', JSON.stringify(openFiles));

    // تمرير التبويب النشط إلى منطقة العرض
    setTimeout(() => {
        const activeTab = tabsContainer.querySelector('.file-tab.active');
        if (activeTab) {
            activeTab.scrollIntoView({ behavior: 'smooth', block: 'nearest', inline: 'center' });
        }
    }, 100);

    // Call our addStatusBar function instead of duplicating code
    addStatusBar();
}

// دالة لإعادة فتح المحرر للملف النشط
function reopenEditor(event) {
    if (event) {
        event.stopPropagation();
    }

    if (activeFileId && workspace.files[activeFileId]) {
        const file = workspace.files[activeFileId];

        // إعادة فتح المحرر بالملف النشط
        document.getElementById('code-executor').classList.add('visible');

        // تحديث المحرر بالمحتوى الحالي
        if (monacoEditor) {
            try {
                // إيجاد أو إنشاء نموذج
                let model = null;
                const existingModels = monaco.editor.getModels();

                if (existingModels && existingModels.length > 0) {
                    model = existingModels.find(m => m.uri && m.uri.path === '/' + activeFileId);
                }

                if (!model) {
                    try {
                        model = monaco.editor.createModel(
                            file.content || '',
                            file.language || 'plaintext',
                            monaco.Uri.parse('inmemory://' + activeFileId)
                        );
                    } catch (e) {
                        console.error('Error creating model:', e);
                        model = monaco.editor.createModel(file.content || '');
                    }
                }

                // تعيين النموذج وتحديثه
                monacoEditor.setModel(model);
                monacoEditor.setValue(file.content || '');

                // محاولة تعيين اللغة
                try {
                    if (file.language && monaco.editor.setModelLanguage) {
                        monaco.editor.setModelLanguage(model, file.language);

                        // تحديث مؤشر اللغة في شريط الحالة
                        const statusItems = document.querySelectorAll('.status-items-right .status-item');
                        if (statusItems.length > 3) {
                            statusItems[3].textContent = file.language.charAt(0).toUpperCase() + file.language.slice(1);
                        }
                    }
                } catch (e) {
                    console.warn('Could not set language:', e);
                }

                // تركيز المحرر
                setTimeout(() => {
                    if (monacoEditor && monacoEditor.focus) {
                        try {
                            monacoEditor.focus();
                        } catch (e) {
                            console.warn('Could not focus editor:', e);
                        }
                    }
                }, 100);
            } catch (e) {
                console.error('Error reopening editor:', e);
            }
        } else {
            // إذا لم يكن المحرر موجودًا، نستخدم openFile
            openFile(activeFileId);
        }

        // تحديث علامات التبويب
        updateFileTabs();
    }
}

// إضافة أنماط CSS للزر الجديد
document.addEventListener('DOMContentLoaded', function () {
    const style = document.createElement('style');
    style.textContent = `
                .file-tab-open {
                    margin-left: 8px;
                    width: 16px;
                    height: 16px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    border-radius: 3px;
                    color: var(--text-dim);
                    font-size: 12px;
                    opacity: 0.7;
                    transition: all 0.2s ease;
                    cursor: pointer;
                }

                .file-tab:hover .file-tab-open {
                    opacity: 1;
                }

                .file-tab-open:hover {
                    color: white;
                    background: rgba(94, 53, 177, 0.5);
                }
            `;
    document.head.appendChild(style);
});

function closeFile(fileId, event) {
    if (event) {
        event.stopPropagation();
    }

    // إزالة الملف من قائمة الملفات المفتوحة
    const openFiles = JSON.parse(localStorage.getItem('openFiles') || '[]');
    const updatedOpenFiles = openFiles.filter(id => id !== fileId);
    localStorage.setItem('openFiles', JSON.stringify(updatedOpenFiles));

    // إذا كان الملف المغلق هو الملف النشط
    if (activeFileId === fileId) {
        // إذا كان هناك ملفات مفتوحة أخرى، نجعل الملف الأول هو النشط
        if (updatedOpenFiles.length > 0) {
            activeFileId = updatedOpenFiles[0];

            // إذا كان المحرر مفتوحًا، نفتح الملف الجديد
            if (document.getElementById('code-executor').classList.contains('visible')) {
                openFile(activeFileId);
            }
        } else {
            // إذا لم يكن هناك ملفات مفتوحة أخرى، نغلق المحرر
            activeFileId = null;
            document.getElementById('code-executor').classList.remove('visible');

            // تنظيف iframe التنفيذ
            if (window._executorIframe && window._executorIframe.parentNode) {
                window._executorIframe.parentNode.removeChild(window._executorIframe);
                window._executorIframe = null;
            }
        }
    }

    updateFileTabs();
    updateFileExplorer();
}

function hideCodeExecutor() {
    document.getElementById('code-executor').classList.remove('visible');
    // لا نقوم بإلغاء تعيين activeFileId هنا لكي تبقى التبويبة نشطة
    // activeFileId = null;

    // تنظيف iframe التنفيذ
    if (window._executorIframe && window._executorIframe.parentNode) {
        window._executorIframe.parentNode.removeChild(window._executorIframe);
        window._executorIframe = null;
    }

    // تحديث مستكشف الملفات لإظهار الملف النشط في قسم المحررات المفتوحة
    updateFileExplorer();
}

async function runCodeInExecutor() {
    const resultElement = document.getElementById('executor-result');
    resultElement.textContent = 'جاري التشغيل...\n';

    // Debug: Check if the terminal element exists
    if (!resultElement) {
        console.error('Terminal element not found (executor-result)');
        return;
    }

    if (!activeFileId || !workspace.files[activeFileId]) {
        resultElement.textContent += 'لا يوجد ملف نشط للتنفيذ.\n';
        return;
    }
    const file = workspace.files[activeFileId];
    const lang = (file.language || '').toLowerCase();

    // Log debugging info
    console.log(`Executing file: ${file.name}, Language: ${lang}`);

    // دعم HTML: عرض النتيجة في نافذة جانبية (web-preview-sidebar)
    if (lang === 'html') {
        resultElement.textContent += 'جاري تحميل المعاينة...\n';

        // احصل على محتوى الملف
        const htmlContent = file.content;

        // تحقق من المصادر المرتبطة (CSS, JS, صور)
        const linkedFiles = findLinkedFiles(htmlContent);
        console.log('الملفات المرتبطة:', linkedFiles);

        // إنشاء خريطة للمصادر المضمنة
        const resourceMap = createResourceMap(linkedFiles);
        console.log('خريطة المصادر:', resourceMap);

        // استبدال روابط المصادر بالمحتوى المضمن أو البيانات المشفرة base64
        const processedHtml = replaceLinkedResources(htmlContent, resourceMap);

        // عرض نافذة المعاينة
        const sidebar = document.getElementById('web-preview-sidebar');
        const iframe = document.getElementById('web-preview-iframe');

        if (sidebar && iframe) {
            // حفظ إعدادات العرض الحالية إذا كانت النافذة مفتوحة
            let currentDeviceType = 'responsive';
            let wasVisible = sidebar.style.display !== 'none';

            if (wasVisible) {
                // حفظ إعدادات الجهاز الحالي
                const deviceSelector = document.getElementById('device-selector');
                if (deviceSelector) {
                    currentDeviceType = deviceSelector.value;
                }

                // حفظ موقع وحجم النافذة
                saveWebPreviewPosition();
            }

            // إظهار النافذة إذا كانت مخفية
            if (sidebar.style.display === 'none') {
                sidebar.style.display = 'flex';
            }

            // تهيئة نافذة المعاينة إذا لم تكن مهيأة بعد
            if (!sidebar.dataset.initialized) {
                initWebPreviewSidebar();
                sidebar.dataset.initialized = 'true';
            }

            // تعيين المحتوى في iframe
            iframe.setAttribute('srcdoc', processedHtml);

            // إضافة مستمع للأحداث لتطبيق إعدادات الجهاز بعد التحميل
            iframe.onload = () => {
                // استرجاع إعدادات الجهاز
                if (wasVisible) {
                    // استخدام نفس إعدادات الجهاز
        setTimeout(() => {
                        const deviceSelector = document.getElementById('device-selector');
                        if (deviceSelector) {
                            deviceSelector.value = currentDeviceType;
                        }
                        changeDeviceView(currentDeviceType);

                        // ضمان ظهور الإطار بالكامل
                        scrollDeviceFrameIntoView();
        }, 100);
                } else {
                    // استرجاع الإعدادات المحفوظة أو استخدام الافتراضية
                    const savedDeviceType = localStorage.getItem('webPreviewDeviceType') || 'responsive';
                    setTimeout(() => {
                        const deviceSelector = document.getElementById('device-selector');
                        if (deviceSelector) {
                            deviceSelector.value = savedDeviceType;
                        }
                        changeDeviceView(savedDeviceType);

                        // ضمان ظهور الإطار بالكامل
                        scrollDeviceFrameIntoView();
                    }, 100);
                }

                // إضافة خيارات للتفاعل مع iframe
                try {
                    // إضافة مستمع للنقرات على الروابط داخل iframe
                    iframe.contentDocument.addEventListener('click', (e) => {
                        const link = e.target.closest('a');
                        if (link && link.href) {
                            // منع الانتقال خارج iframe
                            e.preventDefault();

                            // عرض رسالة في التيرمنال
                            resultElement.textContent += `تم النقر على رابط: ${link.href}\n`;
                            resultElement.textContent += `(الروابط محظورة في وضع المعاينة للأمان)\n`;
                        }
                    });

                    // تعطيل وظائف التحذير والخطأ من داخل iframe
                    iframe.contentWindow.alert = (msg) => {
                        resultElement.textContent += `[تنبيه]: ${msg}\n`;
                    };

                    iframe.contentWindow.onerror = (msg, _source, line) => {
                        resultElement.textContent += `[خطأ في المعاينة]: ${msg} (سطر ${line})\n`;
                        return true; // منع الظهور في وحدة التحكم
                    };

                    // استمع لرسائل console.log
                    const originalConsoleLog = iframe.contentWindow.console.log;
                    iframe.contentWindow.console.log = function() {
                        originalConsoleLog.apply(this, arguments);
                        const args = Array.from(arguments).join(' ');
                        resultElement.textContent += `[console.log]: ${args}\n`;
                    };
                } catch (e) {
                    console.warn('لا يمكن إضافة مستمعي الأحداث إلى iframe:', e);
                }
            };

            resultElement.textContent += 'تم فتح المعاينة!\n';
        } else {
            resultElement.textContent += 'خطأ: لا يمكن العثور على عناصر المعاينة.\n';
        }

        return;
    }

    // دعم جافاسكريبت
    if (["js", "javascript"].includes(lang)) {
        try {
            if (window._executorIframe && window._executorIframe.parentNode) {
                window._executorIframe.parentNode.removeChild(window._executorIframe);
            }
            const iframe = document.createElement('iframe');
            iframe.style.display = 'none';
            document.body.appendChild(iframe);
            window._executorIframe = iframe;
            let context = {
                iframe,
                win: iframe.contentWindow,
                consoleOutput: []
            };

            // Override console methods in the iframe
            try {
            context.win.console.log = function (...args) {
                context.consoleOutput.push(args.join(' '));
                console.log(...args);
            };
            context.win.console.error = function (...args) {
                context.consoleOutput.push('ERROR: ' + args.join(' '));
                console.error(...args);
            };
            context.win.__files = {};
            for (const [, f] of Object.entries(workspace.files)) {
                context.win.__files[f.name] = f.content;
            }
            } catch (e) {
                resultElement.textContent += `خطأ في تهيئة بيئة التنفيذ: ${e.message}\n`;
                console.error('Error setting up execution environment:', e);
                return;
            }

            codeContexts[currentConversationId] = context;
            context.consoleOutput = [];

            const wrappedCode = `
                        try {
                            function require(name) {
                                if (__files[name]) {
                                    const module = { exports: {} };
                                    const func = new Function('module', 'exports', __files[name]);
                                    func(module, module.exports);
                                    return module.exports;
                                }
                                throw new Error('Module not found: ' + name);
                            }
                            ${file.content}
                        } catch(e) {
                            console.error('Error:', e);
                        }
                    `;

            console.log('Executing JavaScript code');
            let result;
            try {
                result = context.win.eval(wrappedCode);
            } catch (e) {
                context.consoleOutput.push(`ERROR: ${e.message}`);
                console.error('Error during evaluation:', e);
            }

            if (result !== undefined) {
                context.consoleOutput.push(result.toString());
            }

            resultElement.textContent += context.consoleOutput.join('\n') || 'تم التنفيذ بنجاح بدون إخراج.\n';
        } catch (e) {
            resultElement.textContent += `خطأ: ${e.message}\n`;
            console.error('Error executing code:', e);
        }
        resultElement.scrollTop = resultElement.scrollHeight;
        return;
    }

    // دعم بايثون
    if (lang === 'python' || lang === 'py') {
        try {
            resultElement.textContent = 'جاري تحميل بايثون (Pyodide)...\n';

            // Check if Pyodide is available
            if (typeof loadPyodide !== 'function') {
                resultElement.textContent += 'خطأ: مكتبة Pyodide غير متوفرة! تأكد من تضمين الملف في الصفحة.\n';
                console.error('Pyodide library not available. Make sure to include the script.');
                return;
            }

            if (!window.pyodide) {
                try {
                window.pyodide = await loadPyodide();
                } catch (e) {
                    resultElement.textContent += `خطأ في تحميل Pyodide: ${e.message}\n`;
                    console.error('Error loading Pyodide:', e);
                    return;
            }
            }

            resultElement.textContent = 'جاري تنفيذ كود بايثون...\n';
            let output = '';

            try {
            window.pyodide.setStdout({
                batched: (s) => { output += s; }
            });
            window.pyodide.setStderr({
                batched: (s) => { output += s; }
            });
            } catch (e) {
                resultElement.textContent += `خطأ في تهيئة المخرجات: ${e.message}\n`;
                console.error('Error setting up stdout/stderr:', e);
            }

            try {
            await window.pyodide.runPythonAsync(file.content);
            resultElement.textContent += output || 'تم التنفيذ بنجاح بدون إخراج.\n';
        } catch (e) {
            resultElement.textContent += `خطأ بايثون: ${e.message}\n`;
                console.error('Python execution error:', e);
            }
        } catch (e) {
            resultElement.textContent += `خطأ: ${e.message}\n`;
            console.error('General error in Python execution:', e);
        }
        resultElement.scrollTop = resultElement.scrollHeight;
        return;
    }

    // لغة غير مدعومة
    resultElement.textContent += `اللغة "${lang}" غير مدعومة. تنفيذ الكود متاح فقط لجافاسكريبت، بايثون، وHTML حالياً.\n`;
}



// إضافة اختصارات لوحة المفاتيح
document.addEventListener('keydown', function (e) {
    const executor = document.getElementById('code-executor');
    if (executor.classList.contains('visible')) {
        if (e.key === 'Escape') {
            hideCodeExecutor(); // فقط إخفاء النافذة دون إغلاق التبويب
            e.preventDefault();
        }

        if (e.key === 'F5') {
            runCodeInExecutor();
            e.preventDefault();
        }
    }

    // اختصار لإرسال الرسالة
    if (e.key === 'Enter' && !e.shiftKey && document.activeElement.id === 'chat-input') {
        e.preventDefault();
        sendMessage();
    }
});

function handleKeyDown(event) {
    if (event.key === 'Enter' && !event.shiftKey) {
        event.preventDefault();
        sendMessage();
    }
}

function toggleSidebar() {
    const sidebar = document.getElementById('sidebar');
    const menuToggle = document.getElementById('menu-toggle');

    sidebar.classList.toggle('visible');
    menuToggle.classList.toggle('active');

    // إزالة أي تأثير على المحتوى الرئيسي
    const mainContent = document.getElementById('main-content');
    mainContent.classList.remove('sidebar-visible');
}

function createNewChat() {
    currentConversationId = Date.now().toString();
    const newConversation = {
        id: currentConversationId,
        title: 'محادثة جديدة',
        messages: [],
        timestamp: new Date().toISOString()
    };

    conversations.unshift(newConversation);
    saveConversations();
    renderConversations();

    // مسح نافذة المحادثة وإضافة شاشة الترحيب
    const chatWindow = document.getElementById('chat-window');
    chatWindow.innerHTML = `
        <!-- شاشة الترحيب الأولية -->
        <div class="welcome-screen" id="welcome-screen">
            <div class="welcome-content">
                <div class="welcome-logo">
                    <div class="logo-icon">
                        <i class="fas fa-code"></i>
                    </div>
                    <h1 class="logo-text">Serinix</h1>
                </div>
                <div class="welcome-message">
                    <h2>مرحباً بك في Serinix</h2>
                    <p>مساعدك الذكي للبرمجة والتطوير</p>
                </div>
                <div class="welcome-suggestions">
                    <div class="suggestion-item" onclick="insertSuggestion('مرحباً، أريد إنشاء موقع ويب جديد')">
                        <i class="fas fa-globe"></i>
                        <span>إنشاء موقع ويب</span>
                    </div>
                    <div class="suggestion-item" onclick="insertSuggestion('أريد تعلم JavaScript من البداية')">
                        <i class="fab fa-js-square"></i>
                        <span>تعلم JavaScript</span>
                    </div>
                    <div class="suggestion-item" onclick="insertSuggestion('ساعدني في حل مشكلة في الكود')">
                        <i class="fas fa-bug"></i>
                        <span>حل مشاكل الكود</span>
                    </div>
                    <div class="suggestion-item" onclick="insertSuggestion('أريد تحسين أداء موقعي')">
                        <i class="fas fa-rocket"></i>
                        <span>تحسين الأداء</span>
                    </div>
                </div>
                <div class="welcome-footer">
                    <p>ابدأ بكتابة رسالتك أدناه أو اختر أحد الاقتراحات</p>
                </div>
            </div>
        </div>
    `;

    // إظهار شاشة الترحيب
    showWelcomeScreen();

    toggleSidebar();
}

// تحديث دالة saveConversations لتحفظ مساحة العمل بشكل منفصل
function saveConversations() {
    localStorage.setItem('conversations', JSON.stringify(conversations));
    saveWorkspace();
}

// دالة جديدة لحفظ مساحة العمل بشكل منفصل
function saveWorkspace() {
    localStorage.setItem('workspace', JSON.stringify(workspace));
    console.log('تم حفظ مساحة العمل:', Object.keys(workspace.files).length, 'ملفات');
}

function loadConversations() {
    const saved = localStorage.getItem('conversations');
    if (saved) {
        conversations = JSON.parse(saved);
        // فرز المحادثات حسب التاريخ من الأحدث
        conversations.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));
        renderConversations();

        // إذا كان هناك محادثات، نفتح الأخيرة
        if (conversations.length > 0) {
            currentConversationId = conversations[0].id;
            loadConversation(currentConversationId);
        }
    }

    // تحميل مساحة العمل
    const savedWorkspace = localStorage.getItem('workspace');
    if (savedWorkspace) {
        workspace = JSON.parse(savedWorkspace);

        // استعادة آخر مسار تم زيارته
        const lastPath = localStorage.getItem('currentPath');
        if (lastPath) {
            workspace.currentPath = lastPath;
        }
    } else {
        // إنشاء هيكل افتراضي للمشروع فقط إذا لم يكن هناك مساحة عمل محفوظة
        createDefaultWorkspace();
    }

    updateFileExplorer();
}

// دالة جديدة لإنشاء هيكل افتراضي للمشروع
function createDefaultWorkspace() {
        workspace.folders['root'] = {
            id: 'root',
            name: 'root',
            path: '/',
            type: 'folder',
            children: []
        };
        createFolder('/project/');
        createFolder('/scripts/');
        createFile('main.js', '// file: /scripts/main.js\nconsole.log("Hello, World!");', 'javascript', '/scripts/');
        createFile('index.html', '<!-- file: /project/index.html -->\n<!DOCTYPE html>\n<html>\n<head>\n    <title>My App</title>\n</head>\n<body>\n    <h1>Welcome</h1>\n</body>\n</html>', 'html', '/project/');
}

function renderConversations() {
    const list = document.getElementById('conversations-list');
    list.innerHTML = '';

    conversations.forEach(conv => {
        // إضافة أيقونة للرسائل الغنية
        const hasRichContent = conv.messages.some(m => m.content.includes('<div class="code-block">'));
        const icon = hasRichContent ? '📄' : '💬';
        const item = document.createElement('div');
        item.className = 'conversation-item';
        item.innerHTML = `
            <div class="conv-title">${icon} ${conv.title}</div>
            <div class="conversation-actions">
                <button class="conversation-action" onclick="deleteConversation('${conv.id}', event)">
                    <i class="fas fa-trash"></i>
                </button>
                <button class="conversation-action" onclick="shareConversation('${conv.id}', event)">
                    <i class="fas fa-share"></i>
                </button>
            </div>
        `;
        item.addEventListener('click', (e) => {
            if (!e.target.closest('.conversation-action')) {
                loadConversation(conv.id);
            }
        });
        list.appendChild(item);
    });
}

async function deleteConversation(id, event) {
    event.stopPropagation();

    const confirmed = await showConfirm(
        'هل تريد حذف هذه المحادثة بشكل دائم؟ لا يمكن التراجع عن هذا الإجراء.',
        'حذف المحادثة'
    );

    if (confirmed) {
        conversations = conversations.filter(conv => conv.id !== id);
        if (currentConversationId === id) {
            currentConversationId = null;
            document.getElementById('chat-window').innerHTML = '';
        }
        saveConversations();
        renderConversations();

        // إظهار إشعار نجاح
        showSuccess('تم حذف المحادثة بنجاح!', 'حذف محادثة');
    }
}

function shareConversation(id, event) {
    event.stopPropagation();
    const conversation = conversations.find(conv => conv.id === id);
    if (conversation) {
        const shareContent = conversation.messages
            .map(m => `${m.role === 'user' ? 'أنت' : 'المساعد'}: ${m.content}`)
            .join('\n\n');

        if (navigator.share) {
            navigator.share({
                title: `محادثة: ${conversation.title}`,
                text: shareContent,
                url: window.location.href
            });
        } else {
            const textArea = document.createElement('textarea');
            textArea.value = shareContent;
            document.body.appendChild(textArea);
            textArea.select();
            document.execCommand('copy');
            document.body.removeChild(textArea);
            showSuccess('تم نسخ المحادثة إلى الحافظة!', 'نسخ ناجح');
        }
    }
}

function loadConversation(id) {
    const conversation = conversations.find(conv => conv.id === id);
    if (conversation) {
        currentConversationId = id;
        const chatWindow = document.getElementById('chat-window');
        chatWindow.innerHTML = '';

        // إذا كانت المحادثة فارغة، أظهر شاشة الترحيب
        if (conversation.messages.length === 0) {
            chatWindow.innerHTML = `
                <!-- شاشة الترحيب الأولية -->
                <div class="welcome-screen" id="welcome-screen">
                    <div class="welcome-content">
                        <div class="welcome-logo">
                            <div class="logo-icon">
                                <i class="fas fa-code"></i>
                            </div>
                            <h1 class="logo-text">Serinix</h1>
                        </div>
                        <div class="welcome-message">
                            <h2>مرحباً بك في Serinix</h2>
                            <p>مساعدك الذكي للبرمجة والتطوير</p>
                        </div>
                        <div class="welcome-suggestions">
                            <div class="suggestion-item" onclick="insertSuggestion('مرحباً، أريد إنشاء موقع ويب جديد')">
                                <i class="fas fa-globe"></i>
                                <span>إنشاء موقع ويب</span>
                            </div>
                            <div class="suggestion-item" onclick="insertSuggestion('أريد تعلم JavaScript من البداية')">
                                <i class="fab fa-js-square"></i>
                                <span>تعلم JavaScript</span>
                            </div>
                            <div class="suggestion-item" onclick="insertSuggestion('ساعدني في حل مشكلة في الكود')">
                                <i class="fas fa-bug"></i>
                                <span>حل مشاكل الكود</span>
                            </div>
                            <div class="suggestion-item" onclick="insertSuggestion('أريد تحسين أداء موقعي')">
                                <i class="fas fa-rocket"></i>
                                <span>تحسين الأداء</span>
                            </div>
                        </div>
                        <div class="welcome-footer">
                            <p>ابدأ بكتابة رسالتك أدناه أو اختر أحد الاقتراحات</p>
                        </div>
                    </div>
                </div>
            `;
            showWelcomeScreen();
            return;
        }

        // تحميل الرسائل الموجودة
        conversation.messages.forEach(msg => {
            const div = document.createElement('div');
            div.className = `message ${msg.role}`;

            if (msg.role === 'user') {
                div.innerHTML = `<div class="message-content">${DOMPurify.sanitize(msg.content, { KEEP_CONTENT: true })}</div>`;
            } else {
                div.innerHTML = msg.content;

                setTimeout(() => {
                    Prism.highlightAllUnder(div);
                    MathJax.typeset([div]);
                    div.querySelectorAll('.copy-button').forEach(btn => {
                        btn.onclick = () => copyCode(btn);
                    });
                    div.querySelectorAll('.run-code-btn').forEach(btn => {
                        btn.onclick = function (e) {
                            e.stopPropagation();
                            const codeBlock = this.closest('.code-block');
                            const lang = codeBlock.querySelector('.language-label').textContent.trim();
                            const content = codeBlock.querySelector('code').textContent;
                            const fileNameMatch = content.match(/\/\/\s*file:\s*([^\n]+)/i) ||
                                content.match(/#\s*file:\s*([^\n]+)/i);
                            const fileName = fileNameMatch && fileNameMatch[1] ?
                                fileNameMatch[1].trim() :
                                `script_${Object.keys(workspace.files).length + 1}.${lang || 'js'}`;
                            showCodeExecutor(codeBlock, fileName, lang, content);
                        };
                    });
                    fixNestedLists();

                    // إزالة أي عناصر SVG خطأ من Mermaid بعد تحميل المحادثة
                    setTimeout(() => {
                        removeMermaidErrorSVGs();
                    }, 500);
                }, 100);
            }

            chatWindow.appendChild(div);
        });

        // إزالة أي عناصر SVG خطأ من Mermaid بعد تحميل جميع الرسائل
        setTimeout(() => {
            removeMermaidErrorSVGs();
        }, 1000);
    }
}

function showErrorAnimation() {
    const menuToggle = document.getElementById('menu-toggle');
    menuToggle.classList.add('error');
    setTimeout(() => menuToggle.classList.remove('error'), 400);
}

function fixNestedLists() {
    document.querySelectorAll('.message').forEach(message => {
        let lists = message.querySelectorAll('ol, ul');

        lists.forEach(list => {
            list.querySelectorAll('li').forEach(li => {
                if (li.children.length === 0 ||
                    Array.from(li.children).every(child => child.nodeName === 'SPAN' || child.nodeName === 'EM' || child.nodeName === 'STRONG')) {
                } else {
                    let newElements = [];
                    Array.from(li.childNodes).forEach(node => {
                        if (node.nodeType === Node.ELEMENT_NODE &&
                            !['OL', 'UL', 'LI', 'SPAN', 'EM', 'STRONG', 'A', 'CODE'].includes(node.nodeName)) {
                            newElements.push(node);
                        }
                    });

                    if (newElements.length > 0) {
                        const parent = li.parentNode;
                        newElements.forEach(el => {
                            parent.insertBefore(el, li.nextSibling);
                        });
                    }
                }
            });
        });

        // إصلاح القوائم المرقمة المكسورة
        message.querySelectorAll('ol').forEach(ol => {
            if (!ol.hasAttribute('data-fixed')) {
                const items = ol.innerHTML.split('</li>');
                if (items.length > 1) {
                    ol.innerHTML = items.map(item => {
                        if (item.trim() && !item.includes('<ol') && !item.includes('<ul')) {
                            return item + '</li>';
                        }
                        return item;
                    }).join('');
                }
                ol.setAttribute('data-fixed', 'true');
            }
        });
    });
}

////////////////////////////////////////////////////////////////////////////////
const promptContent = document.getElementById("chat-input");
const maxH = 80;
promptContent.addEventListener('input', function () {
    this.style.height = 'auto';
    this.style.height = (this.scrollHeight) + 'px';
    if (this.scrollHeight <= maxH) {
        this.style.overflowY = 'hidden';
    } else {
        this.style.height = maxH + 'px';
        this.style.overflowY = 'auto';
    }
});



function escapeHtml(text) {
    return text
        .replace(/</g, "&lt;")
        .replace(/>/g, "&gt;");
}

function toggleThink(header) {
    const container = header.parentElement;
    const content = container.querySelector('.think-content');
    const toggle = container.querySelector('.think-toggle');

    const isOpen = content.style.display !== 'none';

    content.dataset.state = isOpen ? 'closed' : 'open';
    container.dataset.state = isOpen ? 'closed' : 'open';

    toggle.style.transform = isOpen ? 'rotate(0deg)' : 'rotate(90deg)';

    content.style.display = isOpen ? 'none' : 'block';

    if (isOpen) {
        content.style.animation = 'think-close 0.3s ease forwards';
    } else {
        content.style.animation = 'think-open 0.3s ease forwards';

        setTimeout(() => {
            const codeBlocks = content.querySelectorAll('pre code');
            if (codeBlocks.length > 0) {
                Prism.highlightAllUnder(content);
            }

            if (content.querySelector('.math-inline, .math-block')) {
                MathJax.typeset([content]);
            }
        }, 300);
    }
}

function renderMarkdown(text) {

    const thinkRegex = /^&lt;think&gt;([\s\S]*?)&lt;\/think&gt;/gm;
    codeBlockRegex = /```(\w+)?\n([\s\S]*?)```/g;
    inlineCodeRegex = /`([^`]+)`/g;

    const mathStore = [];
    const mathBlockRegex = /\\?(\$\$)([^\$]*?[^\\])(\\?\$\$)/g;
    const mathInlineRegex = /\\?(\$)(?!\$)([^\$]*?[^\\])(\\?\$)/g;
    const codeStore = [];
    const mermaidStore = [];

    text = text
        .replace(codeBlockRegex, (_, lang, content) => {
            // التحقق من كود Mermaid بطرق متعددة
            if (isMermaidCode(lang, content)) {
                mermaidStore.push({ type: 'diagram', content: content.trim() });
                return `@@MERMAID_${mermaidStore.length - 1}@@`;
            }
            codeStore.push({ type: 'block', lang, content });
            return `@@CODE_B_${codeStore.length - 1}@@`;
        });

    text = text
        .replace(mathBlockRegex, (_, _o, c) => {
            mathStore.push({ type: 'block', content: c });
            return `@@MATH_B_${mathStore.length - 1}@@`;
        })
        .replace(mathInlineRegex, (_, _o, c) => {
            mathStore.push({ type: 'inline', content: c });
            return `@@MATH_I_${mathStore.length - 1}@@`;
        });

    text = escapeHtml(text);
    text = DOMPurify.sanitize(text, { ALLOWED_TAGS: [], KEEP_CONTENT: true });

    text = text.replace(/@@CODE_B_(\d+)@@/g, (_, id) => {
        const { lang, content } = codeStore[id];

        // استخراج اسم الملف من التعليق المحسن
        let fileName = `script_${Object.keys(workspace.files).length + 1}.${lang || 'js'}`;
        let filePath = '/';
        const fileMatch = content.match(/\/\/\s*file:\s*([^\n]+)/i) ||
            content.match(/#\s*file:\s*([^\n]+)/i);

        if (fileMatch && fileMatch[1]) {
            const fullPath = fileMatch[1].trim();
            const pathParts = fullPath.split('/');
            fileName = pathParts.pop();
            filePath = pathParts.join('/') + '/';

            // تنظيف المسار إذا كان يبدأ بـ /
            if (filePath.startsWith('/')) {
                filePath = filePath.substring(1);
            }
            if (!filePath.endsWith('/')) {
                filePath += '/';
            }
        }

        // إنشاء الملف مع المسار الصحيح
        const fileId = createFile(fileName, content.trim(), lang || 'javascript', filePath);

        // إضافة زر التشغيل
        const runButton = (lang === 'javascript' || lang === 'js' || lang === 'python')
            ? ``
            : '';

        // إضافة زر فتح في المحرر
        const openButton = `<button class="open-code-btn">
                    <i class="fas fa-code"></i> فتح في المحرر
                </button>`;

        // تخزين المعلومات في data attributes لاستخدامها لاحقًا
        const dataAttrs = `data-filename="${fileName}" data-lang="${lang || 'javascript'}" data-file-id="${fileId}"`;

        let codeDiv = `<div class="code-block" ${dataAttrs}>`;
        codeDiv += `<div class="code-header">`;
        codeDiv += `<span class="language-label">${lang || 'bash'}</span>`;
        codeDiv += `<button class="copy-button" onclick="copyCode(this)">نسخ</button>`;
        codeDiv += openButton;
        codeDiv += runButton;
        codeDiv += `</div>`;
        codeDiv += `<div class="code-content">`;
        codeDiv += `<pre><code class="language-${lang || 'bash'} line-numbers">${content.trim()}</code></pre>`;
        codeDiv += `</div>`;
        codeDiv += `</div>`;
        return codeDiv;
    });

    text = text.replace(inlineCodeRegex, function (_, codeContent) {
        return `<code class="inline-code">${codeContent}</code>`;
    });

    text = text.replace(thinkRegex, function (_, thinkContent) {
        let processedContent = renderMarkdown(thinkContent);
        processedContent = DOMPurify.sanitize(processedContent, {
            ALLOWED_TAGS: ['div', 'span', 'p', 'br', 'b', 'i', 'strong', 'em', 'code', 'pre', 'ul', 'ol', 'li', 'a', 'hr'],
            ALLOWED_ATTR: ['class', 'style', 'href', 'target', 'rel', 'data-animated', 'data-number', 'data-level', 'data-icon', 'data-enhanced'],
        });

        const formattedContent = processedContent.split('\n').map(line => {
            line = line.trim();
            if (!line) return '';
            if (line.startsWith('---')) {
                return '<div class="think-divider"></div>';
            }
            return `<div class="think-step">${line}</div>`;
        }).filter(line => line).join('');

        return `
    <div class="think-container">
        <div class="think-header" onclick="toggleThink(this)">
            <div class="think-toggle">▼</div>
            <div class="think-title">Thinking Process</div>
        </div>
        <div class="think-content">
            ${formattedContent}
        </div>
    </div>`;
    });

    const markdownProcessors = [
        {
            regex: /^(#{1,6})\s(.+?)(?:\n|$)/gm,
            handler: (_, hashes, content) =>
                `<h${hashes.length}>${content}</h${hashes.length}>`
        },
        {
            regex: /\*\*(.+?)\*\*/g,
            handler: (_, content) => `<b>${content}</b>`
        },
        {
            regex: /\*(.+?)\*/g,
            handler: (_, content) => `<em>${content}</em>`
        },
        {
            regex: /^[-*_]{3,}$/gm,
            handler: () => '<hr class="divider">'
        },
        {
            regex: /^(\d+)\.\s(.+)$/gm,
            handler: (_, _num, content) => {
                return `<ol><li>${content}</li></ol>`;
            }
        },
        {
            regex: /^[-*+]\s(.+)$/gm,
            handler: (_, content) => {
                return `<ul><li>${content}</li></ul>`;
            }
        },
        {
            regex: /^>\s(.+)/gm,
            handler: (_, content) => `<blockquote class="blockquote">${content}</blockquote>`
        },
        {
            regex: /\[([^\]]+)\]\(([^)]+)\)/g,
            handler: (_, text, url) => `<a href="${url}" class="link">${text}</a>`
        },
        {
            regex: /!\[([^\]]+)\]\(([^)]+)\)/g,
            handler: (_, alt, src) => `<img src="${src}" alt="${alt}" class="image">`
        },
        {
            regex: /^(\|?[^\n]+\|)(\n\s*\|?[ :-]+\|?[^\n]*)(\n(\|?[^\n]+\|)(\n\|?[^\n]+\|)*)+/gm,
            handler: (_, headers, alignLine, rows) => {
                const alignments = alignLine.split('|')
                    .slice(1, -1)
                    .map(col => {
                        if (/^:-+:$/.test(col)) return 'center';
                        if (/^:-+/.test(col)) return 'left';
                        if (/-+:$/.test(col)) return 'right';
                        return 'left';
                    });

                let html = `
          <div class="table-container">
            <table>
              <thead>
                <tr>
                  ${headers.split('|').slice(1, -1).map((h, i) => `
                    <th style="text-align:${alignments[i]}">${h.trim()}</th>
                  `).join('')}
                </tr>
              </thead>
              <tbody>
                ${rows.trim().split('\n').map(row => `
                  <tr>
                    ${row.split('|').slice(1, -1).map((cell, i) => `
                      <td style="text-align:${alignments[i]}">${cell.trim()}</td>
                    `).join('')}
                  </tr>
                `).join('')}
              </tbody>
            </table>
          </div>`;

                return html;
            }
        }
    ];

    markdownProcessors.forEach(({ regex, handler }) => {
        text = text.replace(regex, handler);
    });

    text = text
        .replace(/^@@MATH_B_(\d+)@@/gm, (_, id) =>
            `<div class="math-block">\\[${mathStore[id].content}\\]</div>`)
        .replace(/^@@MATH_I_(\d+)@@/gm, (_, id) =>
            `<span class="math-inline">\\(${mathStore[id].content}\\)</span>`)
        .replace(/^@@MERMAID_(\d+)@@/gm, (_, id) => {
            const mermaidContent = mermaidStore[id].content;
            const mermaidId = `mermaid-${Date.now()}-${id}`;
            const diagramType = detectMermaidType(mermaidContent);

            // تخزين المحتوى للاستخدام لاحقاً
            window.pendingMermaidDiagrams = window.pendingMermaidDiagrams || [];
            window.pendingMermaidDiagrams.push({
                id: mermaidId,
                content: mermaidContent
            });

            return `<div class="mermaid-container" data-type="${diagramType}" data-mermaid-id="${mermaidId}">
                <div class="mermaid-header">
                    <div class="mermaid-title">
                        <i class="fas fa-project-diagram"></i>
                        مخطط ${getMermaidTypeLabel(diagramType)}
                    </div>
                    <div class="mermaid-controls">
                        <button class="mermaid-btn view-btn active" onclick="toggleMermaidView('${mermaidId}', 'diagram')">
                            <i class="fas fa-eye"></i> عرض
                        </button>
                        <button class="mermaid-btn code-btn" onclick="toggleMermaidView('${mermaidId}', 'code')">
                            <i class="fas fa-code"></i> كود
                        </button>
                        <button class="mermaid-btn copy-btn" onclick="copyMermaidCode('${mermaidId}')">
                            <i class="fas fa-copy"></i> نسخ
                        </button>
                        <div class="mermaid-download-menu">
                            <button class="mermaid-btn download-btn" onclick="toggleDownloadMenu('${mermaidId}')">
                                <i class="fas fa-download"></i> تحميل
                            </button>
                            <div class="mermaid-download-dropdown" id="${mermaidId}-download-menu">
                                <div class="mermaid-download-option" onclick="downloadMermaidDiagram('${mermaidId}', 'png')">
                                    <i class="fas fa-image"></i> PNG
                                </div>
                                <div class="mermaid-download-option" onclick="downloadMermaidDiagram('${mermaidId}', 'svg')">
                                    <i class="fas fa-vector-square"></i> SVG
                                </div>
                                <div class="mermaid-download-option" onclick="downloadMermaidDiagram('${mermaidId}', 'pdf')">
                                    <i class="fas fa-file-pdf"></i> PDF
                                </div>
                            </div>
                        </div>

                        <div class="mermaid-theme-menu">
                            <button class="mermaid-btn theme-btn" onclick="toggleThemeMenu('${mermaidId}')" title="تغيير التصميم">
                                <i class="fas fa-palette"></i>
                            </button>
                            <div class="mermaid-theme-dropdown" id="${mermaidId}-theme-menu">
                                <div class="mermaid-theme-option" onclick="changeMermaidTheme('${mermaidId}', 'default')">
                                    <i class="fas fa-circle" style="color: #4f46e5;"></i> افتراضي
                                </div>
                                <div class="mermaid-theme-option" onclick="changeMermaidTheme('${mermaidId}', 'dark')">
                                    <i class="fas fa-circle" style="color: #1f2937;"></i> داكن
                                </div>
                                <div class="mermaid-theme-option" onclick="changeMermaidTheme('${mermaidId}', 'forest')">
                                    <i class="fas fa-circle" style="color: #059669;"></i> غابة
                                </div>
                                <div class="mermaid-theme-option" onclick="changeMermaidTheme('${mermaidId}', 'neutral')">
                                    <i class="fas fa-circle" style="color: #6b7280;"></i> محايد
                                </div>
                                <div class="mermaid-theme-option" onclick="changeMermaidTheme('${mermaidId}', 'base')">
                                    <i class="fas fa-circle" style="color: #dc2626;"></i> أساسي
                                </div>
                            </div>
                        </div>
                        <button class="mermaid-btn zoom-in-btn" onclick="zoomMermaidDiagram('${mermaidId}', 'in')" title="تكبير">
                            <i class="fas fa-search-plus"></i>
                        </button>
                        <button class="mermaid-btn zoom-out-btn" onclick="zoomMermaidDiagram('${mermaidId}', 'out')" title="تصغير">
                            <i class="fas fa-search-minus"></i>
                        </button>
                        <button class="mermaid-btn zoom-reset-btn" onclick="zoomMermaidDiagram('${mermaidId}', 'reset')" title="إعادة تعيين">
                            <i class="fas fa-expand-arrows-alt"></i>
                        </button>
                        <div class="mermaid-theme-menu">
                            <button class="mermaid-btn theme-btn" onclick="toggleThemeMenu('${mermaidId}')" title="تغيير التصميم">
                                <i class="fas fa-palette"></i>
                            </button>
                            <div class="mermaid-theme-dropdown" id="${mermaidId}-theme-menu">
                                <div class="mermaid-theme-option" onclick="changeMermaidTheme('${mermaidId}', 'default')">
                                    <i class="fas fa-circle" style="color: #0066cc;"></i> افتراضي
                                </div>
                                <div class="mermaid-theme-option" onclick="changeMermaidTheme('${mermaidId}', 'dark')">
                                    <i class="fas fa-circle" style="color: #333;"></i> داكن
                                </div>
                                <div class="mermaid-theme-option" onclick="changeMermaidTheme('${mermaidId}', 'forest')">
                                    <i class="fas fa-circle" style="color: #228B22;"></i> غابة
                                </div>
                                <div class="mermaid-theme-option" onclick="changeMermaidTheme('${mermaidId}', 'neutral')">
                                    <i class="fas fa-circle" style="color: #666;"></i> محايد
                                </div>
                                <div class="mermaid-theme-option" onclick="changeMermaidTheme('${mermaidId}', 'base')">
                                    <i class="fas fa-circle" style="color: #ff6b6b;"></i> أساسي
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="mermaid-content">
                    <div class="mermaid-diagram" id="${mermaidId}-diagram">
                        <div class="mermaid-loading">جاري تحميل المخطط...</div>
                    </div>
                    <div class="mermaid-code" id="${mermaidId}-code">
                        <pre><code class="language-mermaid">${mermaidContent}</code></pre>
                    </div>
                </div>
            </div>`;
        });

    // إصلاح القوائم المتداخلة
    text = text.replace(/<li>([^<]+)<(ul|ol)/g, '<li>$1</li><$2');

    // إغلاق أي قوائم مفتوحة في نهاية النص
    if (text.indexOf('<ol') !== -1 && text.lastIndexOf('<ol') > text.lastIndexOf('</ol>')) {
        text += '</ol>';
    }
    if (text.indexOf('<ul') !== -1 && text.lastIndexOf('<ul') > text.lastIndexOf('</ul>')) {
        text += '</ul>';
    }

    return text
        .replace(/\\\{/g, '{')
        .replace(/\\\}/g, '}')
        .replace(/\\times/g, '×')
        .replace(/\\frac\{([^}]+)\}\{([^}]+)\}/g, '\\frac{$1}{$2}');
}

async function typeRegularMessage(text, elem) {
    let currentText = "";
    const speed = 1;
    for (let i = 0; i < text.length; i++) {
        currentText += text[i];
        elem.innerHTML = DOMPurify.sanitize(renderMarkdown(currentText), { KEEP_CONTENT: true });
        await new Promise(resolve => setTimeout(resolve, speed));
    }
    Prism.highlightAllUnder(elem);
    MathJax.typeset();
    fixNestedLists();

    // معالجة مخططات Mermaid المعلقة
    processPendingMermaidDiagrams();
}

async function typeCodeBlock(content, lang, container) {
    // معالجة خاصة لكود Mermaid - استخدام نفس منطق الكشف
    if (isMermaidCode(lang, content)) {
        const mermaidId = `mermaid-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;
        const diagramType = detectMermaidType(content);

        const mermaidHTML = `<div class="mermaid-container" data-type="${diagramType}" data-mermaid-id="${mermaidId}">
            <div class="mermaid-header">
                <div class="mermaid-title">
                    <i class="fas fa-project-diagram"></i>
                    مخطط ${getMermaidTypeLabel(diagramType)}
                </div>
                <div class="mermaid-controls">
                    <button class="mermaid-btn view-btn active" onclick="toggleMermaidView('${mermaidId}', 'diagram')">
                        <i class="fas fa-eye"></i> عرض
                    </button>
                    <button class="mermaid-btn code-btn" onclick="toggleMermaidView('${mermaidId}', 'code')">
                        <i class="fas fa-code"></i> كود
                    </button>
                    <button class="mermaid-btn copy-btn" onclick="copyMermaidCode('${mermaidId}')">
                        <i class="fas fa-copy"></i> نسخ
                    </button>
                    <div class="mermaid-download-menu">
                        <button class="mermaid-btn download-btn" onclick="toggleDownloadMenu('${mermaidId}')">
                            <i class="fas fa-download"></i> تحميل
                        </button>
                        <div class="mermaid-download-dropdown" id="${mermaidId}-download-menu">
                            <div class="mermaid-download-option" onclick="downloadMermaidDiagram('${mermaidId}', 'png')">
                                <i class="fas fa-image"></i> PNG
                            </div>
                            <div class="mermaid-download-option" onclick="downloadMermaidDiagram('${mermaidId}', 'svg')">
                                <i class="fas fa-vector-square"></i> SVG
                            </div>
                            <div class="mermaid-download-option" onclick="downloadMermaidDiagram('${mermaidId}', 'pdf')">
                                <i class="fas fa-file-pdf"></i> PDF
                            </div>
                        </div>
                        <button class="mermaid-btn zoom-in-btn" onclick="zoomMermaidDiagram('${mermaidId}', 'in')" title="تكبير">
                            <i class="fas fa-search-plus"></i>
                        </button>
                        <button class="mermaid-btn zoom-out-btn" onclick="zoomMermaidDiagram('${mermaidId}', 'out')" title="تصغير">
                            <i class="fas fa-search-minus"></i>
                        </button>
                        <button class="mermaid-btn zoom-reset-btn" onclick="zoomMermaidDiagram('${mermaidId}', 'reset')" title="إعادة تعيين">
                            <i class="fas fa-expand-arrows-alt"></i>
                        </button>
                        <div class="mermaid-theme-menu">
                            <button class="mermaid-btn theme-btn" onclick="toggleThemeMenu('${mermaidId}')" title="تغيير التصميم">
                                <i class="fas fa-palette"></i>
                            </button>
                            <div class="mermaid-theme-dropdown" id="${mermaidId}-theme-menu">
                                <div class="mermaid-theme-option" onclick="changeMermaidTheme('${mermaidId}', 'default')">
                                    <i class="fas fa-circle" style="color: #0066cc;"></i> افتراضي
                                </div>
                                <div class="mermaid-theme-option" onclick="changeMermaidTheme('${mermaidId}', 'dark')">
                                    <i class="fas fa-circle" style="color: #333;"></i> داكن
                                </div>
                                <div class="mermaid-theme-option" onclick="changeMermaidTheme('${mermaidId}', 'forest')">
                                    <i class="fas fa-circle" style="color: #228B22;"></i> غابة
                                </div>
                                <div class="mermaid-theme-option" onclick="changeMermaidTheme('${mermaidId}', 'neutral')">
                                    <i class="fas fa-circle" style="color: #666;"></i> محايد
                                </div>
                                <div class="mermaid-theme-option" onclick="changeMermaidTheme('${mermaidId}', 'base')">
                                    <i class="fas fa-circle" style="color: #ff6b6b;"></i> أساسي
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="mermaid-content">
                <div class="mermaid-diagram" id="${mermaidId}-diagram">
                    <div class="mermaid-loading">جاري تحميل المخطط...</div>
                </div>
                <div class="mermaid-code" id="${mermaidId}-code">
                    <pre><code class="language-mermaid">${content}</code></pre>
                </div>
            </div>
        </div>`;

        container.innerHTML += mermaidHTML;

        // رسم المخطط بعد إضافة HTML
        setTimeout(() => {
            renderMermaidDiagram(mermaidId, content);
        }, 100);

        return;
    }

    // استخراج اسم الملف من التعليقات إن وجد
    let fileName = `script_${Object.keys(workspace.files).length + 1}.${lang || 'js'}`;
    let filePath = '/';
    let fileId = null;

    const fileMatch = content.match(/\/\/\s*file:\s*([^\n]+)/i) ||
        content.match(/#\s*file:\s*([^\n]+)/i);

    if (fileMatch && fileMatch[1]) {
        const fullPath = fileMatch[1].trim();
        const pathParts = fullPath.split('/');
        fileName = pathParts.pop();
        filePath = pathParts.join('/') + '/';

        // تنظيف المسار إذا كان يبدأ بـ /
        if (filePath.startsWith('/')) {
            filePath = filePath.substring(1);
        }
        if (!filePath.endsWith('/')) {
            filePath += '/';
        }
    }

    // إنشاء الملف فقط دون فتحه تلقائيًا
    fileId = createFile(fileName, content.trim(), lang || 'javascript', filePath);

    // إضافة زر التشغيل إذا كانت اللغة مدعومة
    const runButton = (lang === 'javascript' || lang === 'js' || lang === 'python')
        ? `<button class="run-code-btn">
                      <i class="fas fa-play"></i> تشغيل الكود
                   </button>`
        : '';

    // إضافة زر فتح في المحرر
    const openButton = `<button class="open-code-btn">
                <i class="fas fa-code"></i> فتح في المحرر
            </button>`;

    let codeDiv = `<div class="code-block" data-file-id="${fileId}">`;
    codeDiv += `<div class="code-header">`;
    codeDiv += `<span class="language-label">${lang || 'bash'}</span>`;
    codeDiv += `<button class="copy-button" onclick="copyCode(this)">نسخ</button>`;
    codeDiv += openButton;
    codeDiv += runButton;
    codeDiv += `</div>`;
    codeDiv += `<div class="code-content">`;
    codeDiv += `<pre><code class="language-${lang || 'bash'} line-numbers"></code></pre>`;
    codeDiv += `</div>`;
    codeDiv += `</div>`;

    container.innerHTML += codeDiv;

    let codeElement = container.querySelector('.code-block:last-child code');

    // اضافة استجابة لزر التشغيل
    let runBtn = container.querySelector('.code-block:last-child .run-code-btn');
    if (runBtn) {
        runBtn.onclick = function (e) {
            e.stopPropagation();
            const codeBlock = this.closest('.code-block');
            const langVal = codeBlock.querySelector('.language-label').textContent.trim();
            const contentVal = codeBlock.querySelector('code').textContent;
            showCodeExecutor(codeBlock, fileName, langVal, contentVal);
        };
    }

    // اضافة استجابة لزر فتح في المحرر
    let openBtn = container.querySelector('.code-block:last-child .open-code-btn');
    if (openBtn) {
        openBtn.onclick = function (e) {
            e.stopPropagation();
            const codeBlock = this.closest('.code-block');
            const fileId = codeBlock.getAttribute('data-file-id');
            if (fileId) {
                openFile(fileId);
            }
        };
    }

    let currentText = "";
    const speed = 1;
    for (let i = 0; i < content.length; i++) {
        currentText += content[i];
        codeElement.textContent = currentText;
        await new Promise(resolve => setTimeout(resolve, speed));
    }
    Prism.highlightElement(codeElement);
}

async function typeMessage(message, container) {
    const thinkTagRegex = /<think>([\s\S]+?)<\/think>/g;
    let parts = [];
    let lastIndex = 0, match;
    while ((match = thinkTagRegex.exec(message)) !== null) {
        if (match.index > lastIndex) {
            parts.push({ type: 'normal', content: message.substring(lastIndex, match.index) });
        }
        parts.push({ type: 'think', content: match[1] });
        lastIndex = thinkTagRegex.lastIndex;
    }
    if (lastIndex < message.length) {
        parts.push({ type: 'normal', content: message.substring(lastIndex) });
    }

    for (const part of parts) {
        if (part.type === 'normal') {
            const codeRegex = /```(\w+)?\n([\s\S]*?)```/g;
            let idx = 0, codeMatch;
            while ((codeMatch = codeRegex.exec(part.content)) !== null) {
                if (codeMatch.index > idx) {
                    const normalText = part.content.substring(idx, codeMatch.index);
                    const p = document.createElement('p');
                    container.appendChild(p);
                    await typeRegularMessage(normalText, p);
                }
                await typeCodeBlock(codeMatch[2], codeMatch[1] || 'bash', container);
                idx = codeRegex.lastIndex;
            }
            if (idx < part.content.length) {
                const remaining = part.content.substring(idx);
                const p = document.createElement('p');
                container.appendChild(p);
                await typeRegularMessage(remaining, p);
            }
        } else if (part.type === 'think') {
            const thinkBlock = document.createElement("div");
            thinkBlock.className = "think-container";
            thinkBlock.dataset.state = "open";

            thinkBlock.innerHTML = `
    <div class="think-header" onclick="toggleThink(this)">
        <div class="think-toggle">▼</div>
        <div class="think-title">Thinking Process</div>
    </div>
    <div class="think-content" style="display: block;"></div>
`;
            container.appendChild(thinkBlock);

            const thinkContentContainer = thinkBlock.querySelector(".think-content");

            const cleanedContent = part.content
                .replace(/</g, "&lt;")
                .replace(/>/g, "&gt;")
                .trim();

            await typeRegularMessage(cleanedContent, thinkContentContainer);

            setTimeout(() => {
                const steps = thinkContentContainer.innerHTML.split('\n');
                let formattedContent = '';

                steps.forEach(step => {
                    step = step.trim();
                    if (!step) return;

                    if (step.startsWith('---')) {
                        formattedContent += '<div class="think-divider"></div>';
                    } else {
                        formattedContent += `<div class="think-step">${step}</div>`;
                    }
                });

                thinkContentContainer.innerHTML = formattedContent;

                Prism.highlightAllUnder(thinkContentContainer);
                MathJax.typeset([thinkContentContainer]);
            }, 100);
        }
    }
    setTimeout(() => {
        Prism.highlightAllUnder(container);
        MathJax.typeset([container]);
        container.querySelectorAll('.copy-button').forEach(btn => {
            btn.onclick = () => copyCode(btn);
        });

        // تحديث سلوك أزرار التشغيل والفتح في المحرر
        container.querySelectorAll('.run-code-btn').forEach(btn => {
            btn.onclick = (e) => {
                e.stopPropagation();
                const codeBlock = btn.closest('.code-block');
                const lang = codeBlock.querySelector('.language-label').textContent.trim();
                const content = codeBlock.querySelector('code').textContent;
                const fileNameMatch = content.match(/\/\/\s*file:\s*([^\n]+)/i) ||
                    content.match(/#\s*file:\s*([^\n]+)/i);
                const fileName = fileNameMatch && fileNameMatch[1] ?
                    fileNameMatch[1].trim() :
                    `script_${Object.keys(workspace.files).length + 1}.${lang || 'js'}`;
                showCodeExecutor(codeBlock, fileName, lang, content);
            };
        });

        // اضافة استجابة لزر فتح في المحرر
        container.querySelectorAll('.open-code-btn').forEach(btn => {
            btn.onclick = (e) => {
                e.stopPropagation();
                const codeBlock = btn.closest('.code-block');
                const fileId = codeBlock.getAttribute('data-file-id');
                if (fileId) {
                    openFile(fileId);
                }
            };
        });

        fixNestedLists();

        // معالجة مخططات Mermaid المعلقة
        processPendingMermaidDiagrams();

        // إزالة أي عناصر SVG خطأ من Mermaid بعد معالجة المحتوى
        setTimeout(() => {
            removeMermaidErrorSVGs();
        }, 1000);
    }, 1);
}



function createTypingIndicator() {
    const div = document.createElement('div');
    div.className = 'message bot typing-indicator';
    div.innerHTML = `
                <div class="typing-dot"></div>
                <div class="typing-dot"></div>
                <div class="typing-dot"></div>
            `;
    return div;
}


function hideTypingIndicator() {
    var typingIndicator = document.getElementById("typing-indicator");
    if (typingIndicator) {
        typingIndicator.remove();
    }
}

function copyCode(button) {
    const codeBlock = button.closest('.code-block');
    const codeContent = codeBlock.querySelector('code').innerText;
    navigator.clipboard.writeText(codeContent).then(() => {
        const originalText = button.textContent;
        button.textContent = 'تم النسخ!';
        setTimeout(() => button.textContent = originalText, 2000);
    });
}

async function queryGroqAI(userInput) {
    const response = await fetch("https://api.groq.com/openai/v1/chat/completions", {
        method: "POST",
        headers: {
            "Content-Type": "application/json",
            "Authorization": `Bearer ${"********************************************************"}`,
        },
        body: JSON.stringify({
            model: "deepseek-r1-distill-llama-70b",
            messages: [
                {
                    role: "system",
                    content:
                    `أنت مساعد ذكي متخصص في تصميم وتطوير واجهات المستخدم (UI/UX). مهامك الأساسية:

                    ## تخصصك الأساسي:
                    1. **تصميم واجهات المستخدم**: إنشاء تصاميم حديثة وجذابة
                    2. **تجربة المستخدم**: تحسين سهولة الاستخدام والتفاعل
                    3. **التصميم التجاوبي**: ضمان عمل التصميم على جميع الأجهزة
                    4. **إمكانية الوصول**: جعل التصميم متاحاً للجميع
                    5. **الأداء**: تحسين سرعة التحميل والأداء

                    ## عند إنشاء أي كود:
                    - أضف تعليقًا في السطر الأول بالصيغة: // file: [مسار/اسم_ملف.لغة]
                    - **استثناء مهم**: لا تضع تعليق file: في مخططات Mermaid أو أي كود يبدأ بـ graph TD أو flowchart أو sequenceDiagram
                    - استخدم أحدث معايير HTML5, CSS3, JavaScript ES6+
                    - طبق مبادئ التصميم الحديث (Material Design, Fluent Design)
                    - اهتم بالألوان والخطوط والتباعد
                    - استخدم CSS Grid و Flexbox للتخطيط
                    - أضف تأثيرات وانتقالات سلسة
                    - تأكد من التجاوب مع جميع أحجام الشاشات

                    ## أدوات مجانية ستستخدمها:
                    - CSS Frameworks: Bootstrap, Tailwind CSS
                    - Icons: Font Awesome, Feather Icons
                    - Fonts: Google Fonts
                    - Colors: Coolors.co, Adobe Color
                    - Images: Unsplash, Pexels
                    - Animations: Animate.css, AOS

                    ## نصائح التصميم:
                    - استخدم نظام ألوان متناسق
                    - اهتم بالتسلسل الهرمي البصري
                    - استخدم المساحات البيضاء بفعالية
                    - اجعل التصميم بديهي وسهل الاستخدام
                    - اختبر التصميم على أجهزة مختلفة`
                },
                {
                    role: "user",
                    content: userInput
                }],
            temperature: 0.3,
            top_p: 0.9,
            frequency_penalty: 0.2
        }),
    });
    const data = await response.json();
    return data.choices[0].message.content;
}

async function sendMessage() {
    const inputElem = document.getElementById("chat-input");
    const message = inputElem.value.trim();
    if (!message) return;

    // إخفاء شاشة الترحيب عند إرسال أول رسالة
    hideWelcomeScreen();

    const chatWindow = document.getElementById("chat-window");

    if (!currentConversationId) {
        currentConversationId = Date.now().toString();
        conversations.unshift({
            id: currentConversationId,
            title: message.substring(0, 20),
            messages: [],
            timestamp: new Date().toISOString()
        });
        renderConversations();
    }

    const userDiv = document.createElement("div");
    userDiv.className = "message user";
    userDiv.innerHTML = `<div class="message-content">${DOMPurify.sanitize(message, { KEEP_CONTENT: true })}</div>`;
    chatWindow.appendChild(userDiv);

    const currentConv = conversations.find(c => c.id === currentConversationId);
    if (currentConv) {
        currentConv.messages.push({
            role: 'user',
            content: message,
            timestamp: new Date().toISOString()
        });
    }

    userDiv.scrollIntoView({ behavior: "smooth" });
    inputElem.value = "";
    inputElem.style.height = "40px";

    const typingDiv = document.createElement("div");
    typingDiv.className = "message bot typing-indicator";
    typingDiv.innerHTML = `<div class="typing-dot"></div><div class="typing-dot"></div><div class="typing-dot"></div>`;
    chatWindow.appendChild(typingDiv);
    typingDiv.scrollIntoView({ behavior: "smooth" });

    try {
        // فحص وجود أوامر خاصة بالملفات
        let botResponse;

        if (uploadedChatFiles.length > 0) {
            // تحليل الرسالة للبحث عن أوامر
            const detectedCommands = fileCommandProcessor.parseMessage(message);

            if (detectedCommands.length > 0) {
                // تنفيذ الأوامر على الملفات
                botResponse = await fileCommandProcessor.executeCommands(
                    detectedCommands,
                    uploadedChatFiles,
                    message
                );
            } else {
                // إعداد الرسالة مع معلومات الملفات المرفوعة للذكاء الاصطناعي
                let enhancedMessage = message;
                enhancedMessage += '\n\n--- الملفات المرفوعة ---\n';
                uploadedChatFiles.forEach((file, index) => {
                    enhancedMessage += `\nملف ${index + 1}: ${file.name}\n`;
                    enhancedMessage += `النوع: ${file.type}\n`;
                    enhancedMessage += `الحجم: ${file.size}\n`;
                    if (file.content && file.content.length > 0) {
                        enhancedMessage += `المحتوى:\n${file.content.substring(0, 1000)}${file.content.length > 1000 ? '...' : ''}\n`;
                    }
                    enhancedMessage += '---\n';
                });

                botResponse = await queryGroqAI(enhancedMessage);
            }
        } else {
            // رسالة عادية بدون ملفات
            botResponse = await queryGroqAI(message);
        }
        typingDiv.remove();

        const botDiv = document.createElement("div");
        botDiv.className = "message bot";
        chatWindow.appendChild(botDiv);
        await typeMessage(botResponse, botDiv);

        if (currentConv) {
            currentConv.messages.push({
                role: 'bot',
                content: botDiv.innerHTML,
                timestamp: new Date().toISOString()
            });
            currentConv.title = message.substring(0, 20);
        }

        saveConversations();
        renderConversations();
        botDiv.scrollIntoView({ behavior: "smooth" });

        // مسح الملفات المرفوعة بعد الإرسال (اختياري)
        // uploadedChatFiles = [];

    } catch (error) {
        typingDiv.remove();
        console.error("Error:", error);

        if (currentConv) {
            currentConv.messages = currentConv.messages.filter(m => m.content !== message);
        }

        showErrorMessage();
    }
    smartScroll(chatWindow);
}

function smartScroll(element) {
    const threshold = 100;
    const isNearBottom = element.scrollHeight - element.scrollTop <= element.clientHeight + threshold;

    if (isNearBottom) {
        element.scrollTo({
            top: element.scrollHeight,
            behavior: 'smooth'
        });
    }
}

window.addEventListener('resize', () => {
    const activeElement = document.activeElement;
    if (activeElement.tagName === 'TEXTAREA') {
        smartScroll(document.getElementById('chat-window'));
    }
});

function showErrorMessage() {
    // إظهار رسالة خطأ في الدردشة
    const chatWindow = document.getElementById('chat-window');
    const errorDiv = document.createElement('div');
    errorDiv.className = 'message bot error';
    errorDiv.innerHTML = `<div class="message-content">عذراً، حدث خطأ أثناء معالجة طلبك. يرجى المحاولة مرة أخرى.</div>`;
    chatWindow.appendChild(errorDiv);
    smartScroll(chatWindow);

    // إظهار إشعار خطأ تفاعلي أيضاً
    showError('حدث خطأ أثناء معالجة طلبك. يرجى المحاولة مرة أخرى.', 'خطأ في المعالجة');
}

// إدارة شاشة الترحيب
function showWelcomeScreen() {
    const welcomeScreen = document.getElementById('welcome-screen');
    if (welcomeScreen) {
        welcomeScreen.classList.remove('hidden');
        welcomeScreen.style.display = 'flex';
    }
}

function hideWelcomeScreen() {
    const welcomeScreen = document.getElementById('welcome-screen');
    if (welcomeScreen) {
        welcomeScreen.classList.add('hidden');
        setTimeout(() => {
            welcomeScreen.style.display = 'none';
        }, 350); // مدة الانتقال
    }
}

// إدراج اقتراح في حقل الإدخال
function insertSuggestion(text) {
    const chatInput = document.getElementById('chat-input');
    if (chatInput) {
        chatInput.value = text;
        chatInput.focus();
        // تحديث ارتفاع textarea
        chatInput.style.height = 'auto';
        chatInput.style.height = chatInput.scrollHeight + 'px';
    }
}

// التحقق من وجود رسائل في المحادثة
function hasMessages() {
    const chatWindow = document.getElementById('chat-window');
    const messages = chatWindow.querySelectorAll('.message');
    return messages.length > 0;
}

// تهيئة النظام عند التحميل
window.addEventListener('load', () => {
    loadConversations();

    // إظهار شاشة الترحيب إذا لم تكن هناك رسائل
    if (!hasMessages()) {
        showWelcomeScreen();
    }



    // التحقق من وجود العناصر الأساسية للواجهة
    const requiredElements = ['file-explorer', 'sidebar', 'pluginbar'];
    let missingElements = [];

    requiredElements.forEach(id => {
        if (!document.getElementById(id)) {
            missingElements.push(id);
            console.error(`عنصر أساسي مفقود: ${id}`);
        }
    });

    // إضافة عنصر floating-toolbar إذا كان مفقوداً
    if (!document.getElementById('floating-toolbar')) {
        console.log('جاري إنشاء عنصر floating-toolbar المفقود...');
        const floatingToolbar = document.createElement('div');
        floatingToolbar.className = 'floating-toolbar';
        floatingToolbar.id = 'floating-toolbar';
        document.body.appendChild(floatingToolbar);
    }

    // إنشاء هيكل افتراضي للمشروع إذا لم يكن موجوداً
    if (!workspace.folders['root'] || !workspace.folders['root'].children || workspace.folders['root'].children.length === 0) {
        workspace.folders['root'] = {
            id: 'root',
            name: 'root',
            path: '/',
            type: 'folder',
            children: []
        };
        createFolder('/project/');
        createFolder('/scripts/');
        createFile('main.js', '// file: /scripts/main.js\nconsole.log("Hello, World!");', 'javascript', '/scripts/');
        createFile('index.html', '<!-- file: /project/index.html -->\n<!DOCTYPE html>\n<html>\n<head>\n    <title>My App</title>\n</head>\n<body>\n    <h1>Welcome</h1>\n</body>\n</html>', 'html', '/project/');
    }

    // تهيئة قائمة الملفات المفتوحة إذا لم تكن موجودة
    if (!localStorage.getItem('openFiles')) {
        localStorage.setItem('openFiles', JSON.stringify([]));
    }

    // استعادة حالة المستكشف من التخزين المحلي
    const explorerVisible = localStorage.getItem('explorerVisible') === 'true';
    if (explorerVisible) {
        toggleExplorerToolbar(true); // فتح المستكشف بدون تبديل الحالة
    } else {
        // التأكد من أن المستكشف مغلق
        document.getElementById('file-explorer').classList.remove('visible');
        // تحديث العناصر الأخرى للتكيف مع حالة المستكشف المغلق
        document.getElementById('main-content').classList.remove('explorer-visible');
        document.querySelector('.header').classList.remove('explorer-visible');
        document.querySelector('.footer').classList.remove('explorer-visible');
        if (document.getElementById('code-executor')) {
            document.getElementById('code-executor').classList.remove('explorer-visible');
        }
    }

    // إضافة مستمع الأحداث لإغلاق القوائم الجانبية عند الضغط خارجها (للأجهزة اللمسية فقط)
    // استخدام setTimeout لتأخير التفعيل قليلاً لتجنب التداخل مع الأحداث الأخرى
    setTimeout(() => {
        document.addEventListener('touchstart', handleOutsideClick, { passive: true });
        document.addEventListener('click', handleOutsideClick);
    }, 100);

    updateFileExplorer();

    // تهيئة التيرمنال
    initializeTerminal();

    // إضافة المستمعين لأحداث أزرار الكود
    function addCodeButtonListeners() {
        // إضافة المستمعين لأزرار "فتح في المحرر"
        document.querySelectorAll('.open-code-btn').forEach(btn => {
            btn.onclick = (e) => {
                e.stopPropagation();
                const codeBlock = btn.closest('.code-block');
                const fileId = codeBlock.getAttribute('data-file-id');
                if (fileId) {
                    openFile(fileId);
                }
            };
        });

        // إضافة المستمعين لأزرار "تشغيل الكود"
        document.querySelectorAll('.run-code-btn').forEach(btn => {
            btn.onclick = (e) => {
                e.stopPropagation();
                const codeBlock = btn.closest('.code-block');
                const lang = codeBlock.querySelector('.language-label').textContent.trim();
                const content = codeBlock.querySelector('code').textContent;
                const fileNameMatch = content.match(/\/\/\s*file:\s*([^\n]+)/i) ||
                    content.match(/#\s*file:\s*([^\n]+)/i);
                const fileName = fileNameMatch && fileNameMatch[1] ?
                    fileNameMatch[1].trim() :
                    `script_${Object.keys(workspace.files).length + 1}.${lang || 'js'}`;
                showCodeExecutor(codeBlock, fileName, lang, content);
            };
        });
    }

    // تهيئة Tippy.js بأسلوب آمن
    function initTippy() {
        if (typeof tippy !== 'undefined') {
            tippy('.message ol li', {
                content: 'عنصر قائمة مرقمة',
                placement: 'right',
                animation: 'scale',
                theme: 'light',
                delay: [500, 0]
            });

            tippy('.message ul li', {
                content: 'عنصر قائمة نقطية',
                placement: 'right',
                animation: 'scale',
                theme: 'light',
                delay: [500, 0]
            });

            // إضافة tippy لأزرار الكود
            tippy('.open-code-btn', {
                content: 'فتح الملف في المحرر',
                placement: 'top',
                animation: 'scale',
                theme: 'light',
                delay: [300, 0]
            });

            tippy('.run-code-btn', {
                content: 'تشغيل الكود',
                placement: 'top',
                animation: 'scale',
                theme: 'light',
                delay: [300, 0]
            });
        }
    }

    function initSortable() {
        const lists = document.querySelectorAll('.message ul, .message ol');

        lists.forEach(list => {
            if (typeof Sortable !== 'undefined') {
                new Sortable(list, {
                    animation: 150,
                    ghostClass: 'sortable-ghost',
                    chosenClass: 'sortable-chosen',
                    dragClass: 'sortable-drag',
                    disabled: true,
                    onEnd: function (evt) {
                        if (evt.to.tagName.toLowerCase() === 'ol') {
                            const items = evt.to.querySelectorAll('li');
                            items.forEach((item, index) => {
                                item.setAttribute('data-number', index + 1);
                            });
                        }
                    }
                });
            }
        });
    }

    setTimeout(() => {
        initTippy();
        initSortable();
        fixNestedLists();
        addCodeButtonListeners(); // إضافة مستمعي الأحداث لأزرار الكود
    }, 1000);

    const observer = new MutationObserver(function (mutations) {
        mutations.forEach(function (mutation) {
            if (mutation.addedNodes.length) {
                mutation.addedNodes.forEach(node => {
                    if (node.querySelectorAll) {
                        const lists = node.querySelectorAll('ul, ol');
                        const codeButtons = node.querySelectorAll('.open-code-btn, .run-code-btn');
                        if (lists.length || codeButtons.length) {
                            setTimeout(() => {
                                initTippy();
                                initSortable();
                                fixNestedLists();
                                addCodeButtonListeners(); // إضافة مستمعين عند إضافة عناصر جديدة

                                // إزالة أي عناصر SVG خطأ من Mermaid بعد إضافة عناصر جديدة
                                removeMermaidErrorSVGs();
                            }, 500);
                        }
                    }
                });
            }
        });
    });

    observer.observe(document.getElementById('chat-window'), {
        childList: true,
        subtree: true
    });

    // تهيئة نافذة المعاينة إذا كانت مفتوحة
    const sidebar = document.getElementById('web-preview-sidebar');
    if (sidebar && sidebar.style.display !== 'none') {
        initWebPreviewSidebar();
        sidebar.dataset.initialized = 'true';
    }



    setTimeout(() => {
        addCodeButtonListeners();
    }, 1000);
});

// تم إزالة دالة toggleDirection



// نظام الإضافات (Plugins)
let plugins = [
    {
        name: '🎨 مولد لوحة الألوان',
        description: 'إنشاء لوحة ألوان متناسقة للتصميم. استخدم: أنشئ لوحة ألوان [نوع/لون أساسي]',
        enabled: true,
        match: (msg) => /^أنشئ لوحة ألوان|^اعمل لوحة ألوان|^مولد ألوان|^color palette/i.test(msg),
        execute: async (msg) => {
            const colorPalettes = {
                'modern': ['#2563eb', '#3b82f6', '#60a5fa', '#93c5fd', '#dbeafe'],
                'warm': ['#dc2626', '#ef4444', '#f87171', '#fca5a5', '#fecaca'],
                'nature': ['#059669', '#10b981', '#34d399', '#6ee7b7', '#a7f3d0'],
                'sunset': ['#ea580c', '#f97316', '#fb923c', '#fdba74', '#fed7aa'],
                'ocean': ['#0891b2', '#06b6d4', '#22d3ee', '#67e8f9', '#a5f3fc'],
                'purple': ['#7c3aed', '#8b5cf6', '#a78bfa', '#c4b5fd', '#ddd6fe'],
                'gradient': ['#667eea', '#764ba2', '#f093fb', '#f5576c', '#4facfe']
            };

            const type = msg.match(/ألوان\s+(\w+)/)?.[1] || 'modern';
            const palette = colorPalettes[type] || colorPalettes['modern'];

            let response = `<div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 20px; border-radius: 12px; color: white; margin: 10px 0;">`;
            response += `<h3 style="margin: 0 0 15px 0;">🎨 لوحة ألوان ${type}</h3>`;
            response += `<div style="display: flex; flex-wrap: wrap; gap: 10px; margin: 15px 0;">`;

            palette.forEach((color) => {
                response += `<div style="display: flex; flex-direction: column; align-items: center; background: rgba(255,255,255,0.1); padding: 10px; border-radius: 8px;">`;
                response += `<div style="width: 60px; height: 60px; background: ${color}; border-radius: 8px; border: 2px solid rgba(255,255,255,0.3); margin-bottom: 8px;"></div>`;
                response += `<code style="background: rgba(0,0,0,0.3); padding: 4px 8px; border-radius: 4px; font-size: 12px;">${color}</code>`;
                response += `</div>`;
            });

            response += `</div>`;
            response += `<details style="margin-top: 15px;"><summary style="cursor: pointer; font-weight: bold;">📋 كود CSS</summary>`;
            response += `<pre style="background: rgba(0,0,0,0.3); padding: 15px; border-radius: 8px; margin-top: 10px; overflow-x: auto;"><code>:root {\n`;
            palette.forEach((color, index) => {
                response += `  --color-${index + 1}: ${color};\n`;
            });
            response += `}</code></pre></details>`;
            response += `<p style="margin: 15px 0 0 0; font-size: 14px; opacity: 0.9;">💡 الأنواع المتاحة: modern, warm, nature, sunset, ocean, purple, gradient</p>`;
            response += `</div>`;

            return response;
        }
    },
    {
        name: '🧩 مولد مكونات UI',
        description: 'إنشاء مكونات UI جاهزة (أزرار، بطاقات، نماذج). استخدم: أنشئ مكون [نوع المكون]',
        enabled: true,
        match: (msg) => /^أنشئ مكون|^اعمل مكون|^component|^مكون/i.test(msg),
        execute: async (msg) => {
            const componentType = msg.match(/مكون\s+(\w+)/)?.[1] || 'button';

            const components = {
                'button': {
                    name: 'زر حديث',
                    html: `<button class="modern-btn">انقر هنا</button>`,
                    css: `.modern-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  border-radius: 12px;
  color: white;
  padding: 14px 28px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
  position: relative;
  overflow: hidden;
}

.modern-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.modern-btn:active {
  transform: translateY(0);
}`
                },
                'card': {
                    name: 'بطاقة عصرية',
                    html: `<div class="modern-card">
  <div class="card-image">
    <img src="https://via.placeholder.com/300x200" alt="صورة">
  </div>
  <div class="card-content">
    <h3>عنوان البطاقة</h3>
    <p>وصف مختصر للمحتوى...</p>
    <div class="card-actions">
      <button class="btn-primary">إجراء رئيسي</button>
      <button class="btn-secondary">إجراء ثانوي</button>
    </div>
  </div>
</div>`,
                    css: `.modern-card {
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  transition: all 0.3s ease;
  max-width: 350px;
}

.modern-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.card-image img {
  width: 100%;
  height: 200px;
  object-fit: cover;
}

.card-content {
  padding: 24px;
}

.card-content h3 {
  margin: 0 0 12px 0;
  color: #1a1a1a;
  font-size: 20px;
  font-weight: 700;
}

.card-content p {
  color: #666;
  line-height: 1.6;
  margin: 0 0 20px 0;
}

.card-actions {
  display: flex;
  gap: 12px;
}

.btn-primary, .btn-secondary {
  padding: 10px 20px;
  border: none;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-primary {
  background: #667eea;
  color: white;
}

.btn-secondary {
  background: #f1f5f9;
  color: #475569;
}`
                }
            };

            const component = components[componentType] || components['button'];

            let response = `<div style="background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%); padding: 20px; border-radius: 12px; margin: 10px 0;">`;
            response += `<h3 style="margin: 0 0 15px 0; color: #1e293b;">🧩 ${component.name}</h3>`;

            response += `<div style="background: white; padding: 20px; border-radius: 8px; margin: 15px 0; border: 1px solid #e2e8f0;">`;
            response += `<h4 style="margin: 0 0 10px 0; color: #475569;">HTML:</h4>`;
            response += `<pre style="background: #f8fafc; padding: 15px; border-radius: 6px; overflow-x: auto; border: 1px solid #e2e8f0;"><code>${component.html.replace(/</g, '&lt;').replace(/>/g, '&gt;')}</code></pre>`;
            response += `</div>`;

            response += `<div style="background: white; padding: 20px; border-radius: 8px; margin: 15px 0; border: 1px solid #e2e8f0;">`;
            response += `<h4 style="margin: 0 0 10px 0; color: #475569;">CSS:</h4>`;
            response += `<pre style="background: #f8fafc; padding: 15px; border-radius: 6px; overflow-x: auto; border: 1px solid #e2e8f0;"><code>${component.css}</code></pre>`;
            response += `</div>`;

            response += `<p style="margin: 15px 0 0 0; color: #64748b; font-size: 14px;">💡 المكونات المتاحة: button, card, form, navbar, modal, input</p>`;
            response += `</div>`;

            return response;
        }
    },
    {
        name: '📱 مولد تصميم تجاوبي',
        description: 'إنشاء كود CSS للتصميم التجاوبي. استخدم: أنشئ تصميم تجاوبي [نوع التخطيط]',
        enabled: true,
        match: (msg) => /^أنشئ تصميم تجاوبي|^responsive design|^تجاوبي/i.test(msg),
        execute: async (msg) => {
            const layoutType = msg.match(/تجاوبي\s+(\w+)/)?.[1] || 'grid';

            const layouts = {
                'grid': {
                    name: 'تخطيط شبكي',
                    css: `/* تخطيط شبكي تجاوبي */
.responsive-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  padding: 20px;
}

/* للشاشات الكبيرة */
@media (min-width: 1200px) {
  .responsive-grid {
    grid-template-columns: repeat(4, 1fr);
    gap: 30px;
  }
}

/* للشاشات المتوسطة */
@media (max-width: 768px) {
  .responsive-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
    padding: 15px;
  }
}

/* للشاشات الصغيرة */
@media (max-width: 480px) {
  .responsive-grid {
    grid-template-columns: 1fr;
    gap: 10px;
    padding: 10px;
  }
}`
                },
                'flexbox': {
                    name: 'تخطيط مرن',
                    css: `/* تخطيط مرن تجاوبي */
.responsive-flex {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  padding: 20px;
}

.flex-item {
  flex: 1 1 300px;
  min-width: 0;
}

/* للشاشات الكبيرة */
@media (min-width: 1200px) {
  .flex-item {
    flex: 1 1 calc(25% - 15px);
  }
}

/* للشاشات المتوسطة */
@media (max-width: 768px) {
  .responsive-flex {
    gap: 15px;
    padding: 15px;
  }

  .flex-item {
    flex: 1 1 calc(50% - 7.5px);
  }
}

/* للشاشات الصغيرة */
@media (max-width: 480px) {
  .responsive-flex {
    gap: 10px;
    padding: 10px;
  }

  .flex-item {
    flex: 1 1 100%;
  }
}`
                }
            };

            const layout = layouts[layoutType] || layouts['grid'];

            let response = `<div style="background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%); padding: 20px; border-radius: 12px; color: white; margin: 10px 0;">`;
            response += `<h3 style="margin: 0 0 15px 0;">📱 ${layout.name} تجاوبي</h3>`;

            response += `<div style="background: rgba(255,255,255,0.1); padding: 20px; border-radius: 8px; margin: 15px 0;">`;
            response += `<pre style="background: rgba(0,0,0,0.3); padding: 15px; border-radius: 6px; overflow-x: auto; white-space: pre-wrap;"><code>${layout.css}</code></pre>`;
            response += `</div>`;

            response += `<div style="background: rgba(255,255,255,0.1); padding: 15px; border-radius: 8px; margin: 15px 0;">`;
            response += `<h4 style="margin: 0 0 10px 0;">📏 نقاط التوقف المستخدمة:</h4>`;
            response += `<ul style="margin: 0; padding-right: 20px;">`;
            response += `<li>📱 الهواتف: أقل من 480px</li>`;
            response += `<li>📱 التابلت: 481px - 768px</li>`;
            response += `<li>💻 الحاسوب: 769px - 1199px</li>`;
            response += `<li>🖥️ الشاشات الكبيرة: 1200px فأكثر</li>`;
            response += `</ul>`;
            response += `</div>`;

            response += `<p style="margin: 15px 0 0 0; font-size: 14px; opacity: 0.9;">💡 الأنواع المتاحة: grid, flexbox, navbar, sidebar</p>`;
            response += `</div>`;

            return response;
        }
    },
    {
        name: '🎯 محلل إمكانية الوصول',
        description: 'فحص وتحسين إمكانية الوصول للتصميم. استخدم: فحص إمكانية الوصول',
        enabled: true,
        match: (msg) => /^فحص إمكانية الوصول|^accessibility check|^a11y/i.test(msg),
        execute: async () => {
            let response = `<div style="background: linear-gradient(135deg, #10b981 0%, #059669 100%); padding: 20px; border-radius: 12px; color: white; margin: 10px 0;">`;
            response += `<h3 style="margin: 0 0 15px 0;">🎯 دليل إمكانية الوصول</h3>`;

            response += `<div style="background: rgba(255,255,255,0.1); padding: 15px; border-radius: 8px; margin: 15px 0;">`;
            response += `<h4 style="margin: 0 0 10px 0;">✅ قائمة التحقق الأساسية:</h4>`;
            response += `<ul style="margin: 0; padding-right: 20px; line-height: 1.6;">`;
            response += `<li>استخدام نص بديل للصور (alt text)</li>`;
            response += `<li>تباين ألوان كافي (4.5:1 للنص العادي)</li>`;
            response += `<li>إمكانية التنقل بلوحة المفاتيح</li>`;
            response += `<li>استخدام عناوين هرمية (h1, h2, h3...)</li>`;
            response += `<li>تسميات واضحة للنماذج</li>`;
            response += `<li>حجم أهداف اللمس 44px على الأقل</li>`;
            response += `</ul>`;
            response += `</div>`;

            response += `<div style="background: rgba(255,255,255,0.1); padding: 15px; border-radius: 8px; margin: 15px 0;">`;
            response += `<h4 style="margin: 0 0 10px 0;">🛠️ أدوات مجانية للفحص:</h4>`;
            response += `<ul style="margin: 0; padding-right: 20px; line-height: 1.6;">`;
            response += `<li><strong>WAVE:</strong> wave.webaim.org</li>`;
            response += `<li><strong>axe DevTools:</strong> إضافة متصفح مجانية</li>`;
            response += `<li><strong>Lighthouse:</strong> مدمج في Chrome DevTools</li>`;
            response += `<li><strong>Color Contrast Analyzer:</strong> لفحص التباين</li>`;
            response += `</ul>`;
            response += `</div>`;

            response += `<div style="background: rgba(255,255,255,0.1); padding: 15px; border-radius: 8px; margin: 15px 0;">`;
            response += `<h4 style="margin: 0 0 10px 0;">📝 مثال على كود يدعم إمكانية الوصول:</h4>`;
            response += `<pre style="background: rgba(0,0,0,0.3); padding: 15px; border-radius: 6px; overflow-x: auto; white-space: pre-wrap;"><code>&lt;button
  aria-label="إغلاق النافذة"
  class="close-btn"
  tabindex="0"&gt;
  &lt;span aria-hidden="true"&gt;&times;&lt;/span&gt;
&lt;/button&gt;

&lt;img
  src="image.jpg"
  alt="وصف مفصل للصورة"
  role="img"&gt;

&lt;form&gt;
  &lt;label for="email"&gt;البريد الإلكتروني&lt;/label&gt;
  &lt;input
    type="email"
    id="email"
    required
    aria-describedby="email-help"&gt;
  &lt;div id="email-help"&gt;سنستخدم بريدك للتواصل&lt;/div&gt;
&lt;/form&gt;</code></pre>`;
            response += `</div>`;

            response += `</div>`;

            return response;
        }
    },
    {
        name: 'بحث DuckDuckGo',
        description: 'ابحث في الإنترنت بسرعة عبر DuckDuckGo (نتائج فورية مختصرة). اكتب: ابحث في جوجل عن ...',
        enabled: true,
        match: (msg) => /^ابحث في جوجل عن (.+)$/i.test(msg),
        execute: async (msg) => {
            const query = msg.match(/^ابحث في جوجل عن (.+)$/i)[1];
            // DuckDuckGo Instant Answer API (مجاني)
            const url = `https://api.duckduckgo.com/?q=${encodeURIComponent(query)}&format=json&no_redirect=1&no_html=1&skip_disambig=1`;
            const res = await fetch(url);
            const data = await res.json();
            let answer = data.AbstractText || data.Answer || data.Heading || '';
            let link = data.AbstractURL || '';
            let results = '';
            if (answer) results += `<b>النتيجة:</b> ${answer}<br>`;
            if (link) results += `<a href='${link}' target='_blank'>رابط</a><br>`;
            if (!results) results = 'لم يتم العثور على نتيجة مباشرة. جرب سؤالاً آخر.';
            return results;
        }
    },
    {
        name: 'أوامر الملفات الذكية',
        description: 'نفذ أوامر على الملفات والمجلدات مباشرة من الشات: حذف، إنشاء، فتح، إعادة تسمية، عرض محتوى... (مثال: احذف ملف main.js)',
        enabled: true,
        match: (msg) => /^(احذف ملف|أنشئ مجلد|افتح ملف|أعد تسمية ملف|اعرض محتوى ملف)\s+/i.test(msg),
        execute: async (msg) => {
            // حذف ملف
            if (/^احذف ملف (.+)$/i.test(msg)) {
                const fileName = msg.match(/^احذف ملف (.+)$/i)[1].trim();
                const file = Object.values(workspace.files).find(f => f.name === fileName);
                if (file) {
                    delete workspace.files[file.id];
                    for (const folder of Object.values(workspace.folders)) {
                        if (folder && Array.isArray(folder.children)) {
                            const idx = folder.children.indexOf(file.id);
                            if (idx !== -1) folder.children.splice(idx, 1);
                        }
                    }
                    updateFileExplorer();
                    return `✅ تم حذف الملف <b>${fileName}</b> بنجاح.`;
                } else {
                    return `❌ الملف <b>${fileName}</b> غير موجود.`;
                }
            }
            // أنشئ مجلد
            if (/^أنشئ مجلد (.+)$/i.test(msg)) {
                const folderName = msg.match(/^أنشئ مجلد (.+)$/i)[1].trim();
                const newPath = workspace.currentPath + folderName + '/';
                createFolder(newPath);
                updateFileExplorer();
                return `✅ تم إنشاء المجلد <b>${folderName}</b> بنجاح.`;
            }
            // افتح ملف
            if (/^افتح ملف (.+)$/i.test(msg)) {
                const fileName = msg.match(/^افتح ملف (.+)$/i)[1].trim();
                const file = Object.values(workspace.files).find(f => f.name === fileName);
                if (file) {
                    openFile(file.id);
                    return `✅ تم فتح الملف <b>${fileName}</b> في المحرر.`;
                } else {
                    return `❌ الملف <b>${fileName}</b> غير موجود.`;
                }
            }
            // أعد تسمية ملف
            if (/^أعد تسمية ملف (.+) إلى (.+)$/i.test(msg)) {
                const [_, oldName, newName] = msg.match(/^أعد تسمية ملف (.+) إلى (.+)$/i);
                const file = Object.values(workspace.files).find(f => f.name === oldName.trim());
                if (file) {
                    file.name = newName.trim();
                    updateFileExplorer();
                    return `✅ تم تغيير اسم الملف من <b>${oldName}</b> إلى <b>${newName}</b>.`;
                } else {
                    return `❌ الملف <b>${oldName}</b> غير موجود.`;
                }
            }
            // اعرض محتوى ملف
            if (/^اعرض محتوى ملف (.+)$/i.test(msg)) {
                const fileName = msg.match(/^اعرض محتوى ملف (.+)$/i)[1].trim();
                const file = Object.values(workspace.files).find(f => f.name === fileName);
                if (file) {
                    return `<b>محتوى الملف ${fileName}:</b><pre><code>${escapeHtml(file.content)}</code></pre>`;
                } else {
                    return `❌ الملف <b>${fileName}</b> غير موجود.`;
                }
            }
            return '⚠️ لم يتم التعرف على الأمر. جرب: احذف ملف ...، أنشئ مجلد ...، افتح ملف ...، أعد تسمية ملف ...، اعرض محتوى ملف ...';
        }
    },
    {
        name: 'استعراض الملفات',
        description: 'استعرض الملفات المتاحة في المشروع واسمح للمستخدم بفتح ملف مباشرة. استخدم: اعرض الملفات المتاحة',
        enabled: true,
        match: (msg) => /^اعرض الملفات المتاحة$|^اظهر قائمة الملفات$|^عرض الملفات$/i.test(msg),
        execute: async () => {
            const files = Object.values(workspace.files);
            if (files.length === 0) {
                return '❌ لا توجد ملفات في المشروع حاليًا.';
            }

            let result = '<b>الملفات المتاحة في المشروع:</b><br><ul>';
            // ترتيب الملفات حسب المسار ثم الاسم
            files.sort((a, b) => {
                if (a.path !== b.path) return a.path.localeCompare(b.path);
                return a.name.localeCompare(b.name);
            }).forEach(file => {
                // إضافة زر لفتح الملف مباشرة
                result += `<li>${file.path}${file.name} -
                            <button onclick="openFile('${file.id}')"
                                style="background: #5e35b1; color: white; border: none; padding: 2px 6px; border-radius: 4px; font-size: 0.75rem; cursor: pointer;">
                                <i class="fas fa-code"></i> فتح
                            </button>
                        </li>`;
            });
            result += '</ul>';
            return result;
        }
    }
];
function renderPlugins() {
    const list = document.getElementById('pluginbar-list');
    if (!list) return;
    list.innerHTML = '';
    plugins.forEach((plugin, idx) => {
        const item = document.createElement('div');
        item.className = 'plugin-item';
        item.innerHTML = `
            <div class='plugin-name'>${plugin.name}</div>
            <div class='plugin-desc'>${plugin.description}</div>
            <div class='plugin-switch'>
                <label>مفعل<input type='checkbox' ${plugin.enabled ? 'checked' : ''} onchange='togglePlugin(${idx})'></label>
            </div>
        `;
        list.appendChild(item);
    });
}

function togglePlugin(idx) {
    plugins[idx].enabled = !plugins[idx].enabled;
    renderPlugins();
}
// اعتراض إرسال الرسائل لتفعيل الإضافات
const oldSendMessagePlugin = sendMessage;
sendMessage = async function () {
    const inputElem = document.getElementById("chat-input");
    const message = inputElem.value.trim();
    if (!message) return;
    // تحقق من وجود إضافة مناسبة
    const plugin = plugins.find(p => p.enabled && p.match(message));
    if (plugin) {
        // أضف رسالة المستخدم
        const chatWindow = document.getElementById("chat-window");
        const userDiv = document.createElement("div");
        userDiv.className = "message user";
        userDiv.innerHTML = `<div class="message-content">${DOMPurify.sanitize(message, { KEEP_CONTENT: true })}</div>`;
        chatWindow.appendChild(userDiv);
        userDiv.scrollIntoView({ behavior: "smooth" });
        inputElem.value = "";
        inputElem.style.height = "40px";
        // أضف مؤشر انتظار
        const typingDiv = document.createElement("div");
        typingDiv.className = "message bot typing-indicator";
        typingDiv.innerHTML = `<div class="typing-dot"></div><div class="typing-dot"></div><div class="typing-dot"></div>`;
        chatWindow.appendChild(typingDiv);
        typingDiv.scrollIntoView({ behavior: "smooth" });
        try {
            const pluginResult = await plugin.execute(message);
            typingDiv.remove();
            const botDiv = document.createElement("div");
            botDiv.className = "message bot";
            botDiv.innerHTML = `<div class='message-content'>${pluginResult}</div>`;
            chatWindow.appendChild(botDiv);
            botDiv.scrollIntoView({ behavior: "smooth" });
        } catch (e) {
            typingDiv.remove();
            const botDiv = document.createElement("div");
            botDiv.className = "message bot error";
            botDiv.innerHTML = `<div class='message-content'>حدث خطأ أثناء تنفيذ الإضافة.</div>`;
            chatWindow.appendChild(botDiv);
            botDiv.scrollIntoView({ behavior: "smooth" });
        }
        return;
    }
    // إذا لم توجد إضافة مناسبة، نفذ السلوك الافتراضي
    await oldSendMessagePlugin.apply(this, arguments);
}



// توابع toolbar الجديدة
function closeAllSidebars(except) {
    const ids = ['file-explorer', 'sidebar', 'pluginbar'];
    ids.forEach(id => {
        if (id !== except) {
            const el = document.getElementById(id);
            if (el) el.classList.remove('visible');
            else console.warn(`العنصر ${id} غير موجود في DOM`);
        }
    });
}

// دالة للتحقق من كون الجهاز لمسي
function isTouchDevice() {
    return (('ontouchstart' in window) ||
            (navigator.maxTouchPoints > 0) ||
            (navigator.msMaxTouchPoints > 0));
}

// متغير لتتبع آخر نقرة داخل المستكشف
let lastExplorerInteraction = 0;

// دالة إغلاق القوائم الجانبية عند الضغط خارجها (للأجهزة اللمسية فقط)
function handleOutsideClick(event) {
    // التحقق من كون الجهاز لمسي
    if (!isTouchDevice()) {
        return;
    }

    const target = event.target;
    const currentTime = Date.now();

    // تجاهل النقرات إذا كانت قريبة جداً من آخر تفاعل مع المستكشف
    if (currentTime - lastExplorerInteraction < 300) {
        return;
    }

    // تجاهل النقرات على أزرار التبديل والقوائم نفسها وجميع عناصرها الفرعية
    if (target.closest('#sidebar, #file-explorer, #pluginbar, #menu-toggle, #explorer-toggle, #pluginbar-toggle')) {
        lastExplorerInteraction = currentTime;
        return;
    }

    // تجاهل النقرات على عناصر المستكشف المحددة
    if (target.closest('.explorer-item, .explorer-action, .explorer-header, .explorer-content, .conversation-item, .conversation-actions, .explorer-item-icon, .explorer-item-name, .explorer-item-actions, .explorer-item-action, .breadcrumb, .breadcrumb-item, .breadcrumb-separator')) {
        lastExplorerInteraction = currentTime;
        return;
    }

    // تجاهل النقرات التي تحتوي على onclick أو أحداث أخرى داخل القوائم
    if (target.onclick || target.getAttribute('onclick')) {
        const parentExplorer = target.closest('#file-explorer, #sidebar, #pluginbar');
        if (parentExplorer) {
            lastExplorerInteraction = currentTime;
            return;
        }
    }

    // تجاهل النقرات على العناصر التي تحتوي على data attributes خاصة بالمستكشف
    if (target.getAttribute('data-file-id') || target.getAttribute('data-folder-id') || target.closest('[data-file-id], [data-folder-id]')) {
        lastExplorerInteraction = currentTime;
        return;
    }

    // التحقق من القوائم الجانبية المفتوحة
    const sidebar = document.getElementById('sidebar');
    const fileExplorer = document.getElementById('file-explorer');
    const pluginbar = document.getElementById('pluginbar');

    let anySidebarClosed = false;

    // إغلاق sidebar إذا كان مفتوحاً
    if (sidebar && sidebar.classList.contains('visible')) {
        sidebar.classList.remove('visible');
        const menuToggleBtn = document.getElementById('menu-toggle');
        if (menuToggleBtn) {
            menuToggleBtn.classList.remove('active');
        }
        anySidebarClosed = true;
    }

    // إغلاق file-explorer إذا كان مفتوحاً
    if (fileExplorer && fileExplorer.classList.contains('visible')) {
        fileExplorer.classList.remove('visible');
        const explorerToggleBtn = document.getElementById('explorer-toggle');
        if (explorerToggleBtn) {
            explorerToggleBtn.classList.remove('active');
        }
        // تحديث حالة المحتوى
        const container = document.getElementById('main-content');
        const codeExecutor = document.getElementById('code-executor');
        if (container) container.classList.remove('explorer-visible');
        if (codeExecutor) codeExecutor.classList.remove('explorer-visible');
        localStorage.setItem('explorerVisible', 'false');
        anySidebarClosed = true;
    }

    // إغلاق pluginbar إذا كان مفتوحاً
    if (pluginbar && pluginbar.classList.contains('visible')) {
        pluginbar.classList.remove('visible');
        const pluginbarToggleBtn = document.getElementById('pluginbar-toggle');
        if (pluginbarToggleBtn) {
            pluginbarToggleBtn.classList.remove('active');
        }
        anySidebarClosed = true;
    }

    // منع الحدث الافتراضي إذا تم إغلاق أي قائمة جانبية
    if (anySidebarClosed) {
        event.preventDefault();
        event.stopPropagation();
    }
}

// تحديث دالة تبديل المستكشف
function toggleExplorerToolbar(skipToggle) {
    closeAllSidebars('file-explorer');

    const explorer = document.getElementById('file-explorer');
    const container = document.getElementById('main-content');
    const codeExecutor = document.getElementById('code-executor');

    if (!skipToggle) {
        explorer.classList.toggle('visible');
    }
    const isExplorerVisible = explorer.classList.contains('visible');
    localStorage.setItem('explorerVisible', isExplorerVisible ? 'true' : 'false');

    // Still apply classes but don't shift content
    container.classList.toggle('explorer-visible', isExplorerVisible);
    if (codeExecutor) {
        codeExecutor.classList.toggle('explorer-visible', isExplorerVisible);
    }

    // Update toggle button state
    const explorerToggleBtn = document.getElementById('explorer-toggle');
    if (explorerToggleBtn) {
        explorerToggleBtn.classList.toggle('active', isExplorerVisible);
    }
}

function toggleSidebarToolbar() {
    closeAllSidebars('sidebar');
    document.getElementById('sidebar').classList.toggle('visible');
    // تفعيل/تعطيل الزر
    const menuToggleBtn = document.getElementById('menu-toggle');
    if (menuToggleBtn) {
        menuToggleBtn.classList.toggle('active', document.getElementById('sidebar').classList.contains('visible'));
    }
}

function togglePluginbarToolbar() {
    closeAllSidebars('pluginbar');
    document.getElementById('pluginbar').classList.toggle('visible');
    renderPlugins();
    // تفعيل/تعطيل الزر
    const pluginbarToggleBtn = document.getElementById('pluginbar-toggle');
    if (pluginbarToggleBtn) {
        pluginbarToggleBtn.classList.toggle('active', document.getElementById('pluginbar').classList.contains('visible'));
    }
}



// إضافة دالة لإغلاق جميع الملفات
function closeAllFiles() {
    // تفريغ قائمة الملفات المفتوحة
    localStorage.setItem('openFiles', JSON.stringify([]));

    activeFileId = null;
    document.getElementById('code-executor').classList.remove('visible');

    // تنظيف iframe التنفيذ
    if (window._executorIframe && window._executorIframe.parentNode) {
        window._executorIframe.parentNode.removeChild(window._executorIframe);
        window._executorIframe = null;
    }

    updateFileTabs();
    updateFileExplorer();
}

// دالة إغلاق نافذة المعاينة الجانبية
function closeWebPreview() {
    const sidebar = document.getElementById('web-preview-sidebar');
    if (sidebar) {
        sidebar.style.display = 'none';

        // إعادة تعيين حالة النافذة عند الإغلاق
        sidebar.classList.remove('minimized', 'maximized');

        // إيقاف المعاينة
        const iframe = document.getElementById('web-preview-iframe');
        if (iframe && iframe.contentWindow) {
            iframe.src = 'about:blank';
        }

        // حفظ حالة الإغلاق
        localStorage.setItem('webPreviewMinimized', false);
        localStorage.setItem('webPreviewMaximized', false);
    }
}

// دالة تهيئة نافذة المعاينة الجانبية
function initWebPreviewSidebar() {
    const sidebar = document.getElementById('web-preview-sidebar');
    if (!sidebar) return;

    // إضافة مستمعي الأحداث للأزرار
    document.getElementById('preview-refresh').addEventListener('click', refreshWebPreview);
    document.getElementById('preview-minimize').addEventListener('click', minimizeWebPreview);
    document.getElementById('preview-maximize').addEventListener('click', maximizeWebPreview);
    document.getElementById('preview-close').addEventListener('click', closeWebPreview);

    // مستمع حدث لتغيير الجهاز
    const deviceSelector = document.getElementById('device-selector');
    if (deviceSelector) {
        deviceSelector.addEventListener('change', function(e) {
            changeDeviceView(e);
            // تمرير الإطار إلى العرض بعد تغيير الجهاز
            scrollDeviceFrameIntoView();
        });
    }

    // جعل النافذة قابلة للسحب
    makeDraggable(sidebar, document.querySelector('.preview-drag-handle'));

    // إضافة مستمع لتغيير الحجم
    const resizeHandle = document.getElementById('preview-resize-handle');
    if (resizeHandle) {
        resizeHandle.addEventListener('mousedown', initResize);
    }

    // استعادة الإعدادات المحفوظة
    restoreWebPreviewSettings();

    // إضافة مستمع لتغيير حجم النافذة لضمان التجاوب
    window.addEventListener('resize', adjustWebPreviewForScreenSize);
}

// دالة تحديث معاينة الويب
function refreshWebPreview() {
    // إعادة توليد المعاينة كما في تشغيل كود HTML
    runCodeInExecutor();
}

// دالة ضبط حجم نافذة المعاينة بناءً على حجم الشاشة
function adjustWebPreviewForScreenSize() {
    const sidebar = document.getElementById('web-preview-sidebar');
    if (!sidebar || sidebar.style.display === 'none') return;

    // إذا كانت النافذة في وضع التكبير أو التصغير، لا نقوم بأي تغيير
    if (sidebar.classList.contains('maximized') || sidebar.classList.contains('minimized')) {
        return;
    }

    const windowWidth = window.innerWidth;

    // إخفاء device-selector على الشاشات الصغيرة
    const deviceSelectWrapper = document.querySelector('.device-select-wrapper');
    if (deviceSelectWrapper) {
        if (windowWidth <= 700) {
            deviceSelectWrapper.style.display = 'none';
        } else {
            deviceSelectWrapper.style.display = '';
        }
    }

    // ضبط حجم وموضع النافذة بناءً على حجم الشاشة
    if (windowWidth <= 576) {
        // الأجهزة الصغيرة جدًا (الهواتف)
        sidebar.style.width = '100%';
        sidebar.style.height = '100%';
        sidebar.style.top = '0';
        sidebar.style.left = '0';
    } else if (windowWidth <= 768) {
        // الأجهزة الصغيرة (الهواتف الكبيرة)
        sidebar.style.width = '95%';
        sidebar.style.height = '90%';
        sidebar.style.top = '5%';
        sidebar.style.left = '2.5%';
    } else if (windowWidth <= 992) {
        // الأجهزة المتوسطة (التابلت)
        sidebar.style.width = '90%';
        sidebar.style.height = 'calc(100% - 40px)';
        sidebar.style.top = '20px';
        sidebar.style.left = '5%';
    } else if (windowWidth <= 1200) {
        // الأجهزة الكبيرة (الحواسيب المحمولة)
        sidebar.style.width = '80%';
        sidebar.style.height = 'calc(100% - 40px)';
        sidebar.style.top = '20px';
        sidebar.style.left = '10%';
    } else {
        // الأجهزة الكبيرة جدًا (الحواسيب المكتبية)
        sidebar.style.width = '480px';
        sidebar.style.height = 'calc(100% - 40px)';
        sidebar.style.top = '20px';
        sidebar.style.left = '20px';
    }

    // جعل iframe يأخذ كل المساحة المتاحة دائماً
    const iframe = document.getElementById('web-preview-iframe');
    if (iframe) {
        iframe.style.width = '100%';
        iframe.style.height = '100%';
        iframe.style.position = 'absolute';
        iframe.style.top = '0';
        iframe.style.left = '0';
    }
}

// تعديل دالة تشغيل كود HTML لضبط حجم المعاينة بعد عرضها
async function runHtmlCode(code) {
    try {
        // إنشاء blob من كود HTML
        const blob = new Blob([code], { type: 'text/html' });
        const url = URL.createObjectURL(blob);

        // البحث عن الملفات المرتبطة (CSS، JS، الصور)
        const linkedFiles = findLinkedFiles(code);

        // إنشاء خريطة الموارد
        const resourceMap = createResourceMap(linkedFiles);

        // استبدال روابط الموارد في كود HTML
        const modifiedHtml = replaceLinkedResources(code, resourceMap);

        // إضافة CSS لضمان تجاوب المحتوى
        const responsiveMetaTag = '<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">';
        const responsiveCSS = `
        <style>
            html, body {
                margin: 0;
                padding: 0;
                width: 100%;
                height: 100%;
                overflow: auto;
            }
            * {
                box-sizing: border-box;
            }
        </style>
        `;

        // إدراج meta viewport و CSS في الكود
        let finalHtml = modifiedHtml;
        if (!finalHtml.includes('<meta name="viewport"')) {
            finalHtml = finalHtml.replace('</head>', `${responsiveMetaTag}\n</head>`);
        }
        if (!finalHtml.includes('box-sizing: border-box')) {
            finalHtml = finalHtml.replace('</head>', `${responsiveCSS}\n</head>`);
        }

        // إنشاء blob جديد بالكود المعدل
        const modifiedBlob = new Blob([finalHtml], { type: 'text/html' });
        const modifiedUrl = URL.createObjectURL(modifiedBlob);

        // عرض النتيجة في نافذة المعاينة الجانبية
        const sidebar = document.getElementById('web-preview-sidebar');
        if (sidebar) {
            sidebar.style.display = 'flex';

            // ضبط حجم النافذة بناءً على حجم الشاشة
            adjustWebPreviewForScreenSize();

            // تحميل المعاينة في الإطار
            const iframe = document.getElementById('web-preview-iframe');
            if (iframe) {
                // إظهار مؤشر التحميل
                document.getElementById('preview-loading').classList.add('active');

                // تعيين المحتوى في الإطار
                iframe.src = modifiedUrl;

                // إضافة مستمع لحدث تحميل الإطار
                iframe.onload = function() {
                    // إخفاء مؤشر التحميل
                    document.getElementById('preview-loading').classList.remove('active');

                    // إضافة مستمع لتغيير حجم النافذة داخل الإطار
                    try {
                        if (iframe.contentWindow) {
                            iframe.contentWindow.addEventListener('resize', function() {
                                // تحديث أبعاد الجهاز في واجهة المستخدم
                                updateDeviceDimensions();
                            });
                        }

                        // تطبيق إعدادات الجهاز الحالي
                        const deviceSelector = document.getElementById('device-selector');
                        if (deviceSelector) {
                            const currentDevice = deviceSelector.value;
                            setTimeout(() => {
                                changeDeviceView(currentDevice);
                            }, 100);
                        }
                    } catch (e) {
                        console.warn('خطأ في إضافة مستمع الحجم للإطار:', e);
                    }
                };

                // تعيين onerror للإطار
                iframe.onerror = function() {
                    document.getElementById('preview-loading').classList.remove('active');
                    console.error('فشل تحميل المعاينة');

                    // إضافة رسالة خطأ في التيرمنال
                    const terminalElem = document.getElementById('executor-result');
                    if (terminalElem) {
                        terminalElem.innerHTML += '<div class="terminal-message error">فشل تحميل المعاينة</div>';
                    }
                };
            }
        } else {
            // إذا لم تكن نافذة المعاينة موجودة، نعرض النتيجة في نافذة جديدة
            window.open(url, '_blank');
        }

        // إضافة رسالة نجاح في التيرمنال
        const terminalElem = document.getElementById('executor-result');
        if (terminalElem) {
            terminalElem.innerHTML += '<div class="terminal-message success">تم تشغيل كود HTML بنجاح. تم فتح المعاينة.</div>';
        }
    } catch (error) {
        console.error('خطأ في تشغيل كود HTML:', error);

        // إضافة رسالة خطأ في التيرمنال
        const terminalElem = document.getElementById('executor-result');
        if (terminalElem) {
            terminalElem.innerHTML += `<div class="terminal-message error">خطأ في تشغيل كود HTML: ${error.message}</div>`;
        }
    }
}

// دالة جعل العنصر قابل للسحب
function makeDraggable(element, handle) {
    let pos1 = 0, pos2 = 0, pos3 = 0, pos4 = 0;

    if (handle) {
        // إذا تم تحديد مقبض، استخدمه للسحب
        handle.onmousedown = dragMouseDown;
        // استخدام addEventListener مع passive: false بدلاً من ontouchstart
        handle.addEventListener('touchstart', dragTouchStart, { passive: false });
    } else {
        // وإلا استخدم العنصر نفسه
        element.onmousedown = dragMouseDown;
        // استخدام addEventListener مع passive: false بدلاً من ontouchstart
        element.addEventListener('touchstart', dragTouchStart, { passive: false });
    }

    function dragMouseDown(e) {
        e = e || window.event;
        e.preventDefault();

        // لا تسمح بالسحب إذا كانت النافذة مكبرة
        if (element.classList.contains('maximized')) {
            return;
        }

        // الحصول على موضع الماوس عند بدء السحب
        pos3 = e.clientX;
        pos4 = e.clientY;
        document.onmouseup = closeDragElement;
        document.onmousemove = elementDrag;
    }

    // دعم اللمس للأجهزة المحمولة
    function dragTouchStart(e) {
        // لا تسمح بالسحب إذا كانت النافذة مكبرة
        if (element.classList.contains('maximized')) {
            return;
        }

        // الحصول على موضع اللمس الأول
        const touch = e.touches[0];
        pos3 = touch.clientX;
        pos4 = touch.clientY;

        // إضافة مستمعي أحداث اللمس مع passive: false
        document.addEventListener('touchend', closeTouchDragElement, { passive: true });
        document.addEventListener('touchmove', elementTouchDrag, { passive: false });

        // منع السلوك الافتراضي (مثل التمرير)
        e.preventDefault();
    }

    function elementDrag(e) {
        e = e || window.event;
        e.preventDefault();

        // حساب الموضع الجديد
        pos1 = pos3 - e.clientX;
        pos2 = pos4 - e.clientY;
        pos3 = e.clientX;
        pos4 = e.clientY;

        // حساب الحدود
        const windowWidth = window.innerWidth;
        const windowHeight = window.innerHeight;

        // الحصول على الموضع الحالي
        let newTop = element.offsetTop - pos2;
        let newLeft = element.offsetLeft - pos1;

        // التأكد من أن العنصر لا يخرج من حدود النافذة
        newTop = Math.max(0, Math.min(newTop, windowHeight - 40));
        newLeft = Math.max(0, Math.min(newLeft, windowWidth - 40));

        // تحديث موضع العنصر
        element.style.top = newTop + "px";
        element.style.left = newLeft + "px";

        // حفظ الموضع الجديد
        if (element.id === 'web-preview-sidebar') {
            saveWebPreviewPosition();
        }
    }

    // دعم السحب باللمس
    function elementTouchDrag(e) {
        // منع السلوك الافتراضي
        e.preventDefault();

        // الحصول على موضع اللمس الحالي
        const touch = e.touches[0];

        // حساب الموضع الجديد
        pos1 = pos3 - touch.clientX;
        pos2 = pos4 - touch.clientY;
        pos3 = touch.clientX;
        pos4 = touch.clientY;

        // حساب الحدود
        const windowWidth = window.innerWidth;
        const windowHeight = window.innerHeight;

        // الحصول على الموضع الحالي
        let newTop = element.offsetTop - pos2;
        let newLeft = element.offsetLeft - pos1;

        // التأكد من أن العنصر لا يخرج من حدود النافذة
        newTop = Math.max(0, Math.min(newTop, windowHeight - 40));
        newLeft = Math.max(0, Math.min(newLeft, windowWidth - 40));

        // تحديث موضع العنصر
        element.style.top = newTop + "px";
        element.style.left = newLeft + "px";

        // حفظ الموضع الجديد
        if (element.id === 'web-preview-sidebar') {
            saveWebPreviewPosition();
        }
    }

    function closeDragElement() {
        // إيقاف تحريك العنصر عند ترك زر الماوس
        document.onmouseup = null;
        document.onmousemove = null;
    }

    // إيقاف السحب باللمس
    function closeTouchDragElement() {
        document.removeEventListener('touchend', closeTouchDragElement);
        document.removeEventListener('touchmove', elementTouchDrag);
    }
}

// دالة بدء تغيير الحجم
function initResize(e) {
    e.preventDefault();

    const sidebar = document.getElementById('web-preview-sidebar');
    if (sidebar.classList.contains('maximized')) return;

    const startX = e.clientX;
    const startY = e.clientY;
    const startWidth = sidebar.offsetWidth;
    const startHeight = sidebar.offsetHeight;

    document.addEventListener('mousemove', doResize);
    document.addEventListener('mouseup', stopResize);

    function doResize(e) {
        const newWidth = startWidth + (e.clientX - startX);
        const newHeight = startHeight + (e.clientY - startY);

        // تطبيق الحجم الجديد مع مراعاة الحد الأدنى
        sidebar.style.width = Math.max(320, newWidth) + 'px';
        sidebar.style.height = Math.max(200, newHeight) + 'px';

        // حفظ الحجم الجديد
        saveWebPreviewPosition();
    }

    function stopResize() {
        document.removeEventListener('mousemove', doResize);
        document.removeEventListener('mouseup', stopResize);
    }
}

// دالة حفظ موضع وحجم نافذة المعاينة
function saveWebPreviewPosition() {
    const sidebar = document.getElementById('web-preview-sidebar');
    if (!sidebar) return;

    // حفظ الموقع والحجم
    localStorage.setItem('webPreviewTop', sidebar.style.top);
    localStorage.setItem('webPreviewLeft', sidebar.style.left);
    localStorage.setItem('webPreviewWidth', sidebar.style.width);
    localStorage.setItem('webPreviewHeight', sidebar.style.height);

    // حفظ حالة التصغير/التكبير
    localStorage.setItem('webPreviewMaximized', sidebar.classList.contains('maximized'));
    localStorage.setItem('webPreviewMinimized', sidebar.classList.contains('minimized'));

    console.log('تم حفظ موضع نافذة المعاينة:', {
        top: sidebar.style.top,
        left: sidebar.style.left,
        width: sidebar.style.width,
        height: sidebar.style.height,
        isMaximized: sidebar.classList.contains('maximized'),
        isMinimized: sidebar.classList.contains('minimized')
    });
}

// دالة إعادة تعيين موضع نافذة المعاينة
function resetWebPreviewPosition() {
    const sidebar = document.getElementById('web-preview-sidebar');
    if (sidebar) {
        sidebar.style.top = '20px';
        sidebar.style.left = '20px';
        sidebar.style.width = '480px';
        sidebar.style.height = 'calc(100% - 40px)';
    }
}

// دالة استعادة إعدادات نافذة المعاينة
function restoreWebPreviewSettings() {
    // استرجاع موضع النافذة
    const sidebar = document.getElementById('web-preview-sidebar');
    if (!sidebar) return;

    const savedTop = localStorage.getItem('webPreviewTop');
    const savedLeft = localStorage.getItem('webPreviewLeft');
    const savedWidth = localStorage.getItem('webPreviewWidth');
    const savedHeight = localStorage.getItem('webPreviewHeight');
    const deviceType = localStorage.getItem('webPreviewDeviceType') || 'responsive';

    // استرجاع الحجم والموضع إذا كانت محفوظة
    if (savedTop && savedLeft) {
        sidebar.style.top = savedTop;
        sidebar.style.left = savedLeft;
    } else {
        // وضع افتراضي إذا لم تكن محفوظة
        sidebar.style.top = '20px';
        sidebar.style.left = '20px';
    }

    if (savedWidth && savedHeight) {
        sidebar.style.width = savedWidth;
        sidebar.style.height = savedHeight;
    } else {
        // حجم افتراضي إذا لم يكن محفوظاً
        sidebar.style.width = '480px';
        sidebar.style.height = 'calc(100% - 40px)';
    }

    // استرجاع حالة التصغير/التكبير
    const isMaximized = localStorage.getItem('webPreviewMaximized') === 'true';
    const isMinimized = localStorage.getItem('webPreviewMinimized') === 'true';

    if (isMaximized) {
        sidebar.classList.add('maximized');
    } else if (isMinimized) {
        sidebar.classList.add('minimized');
    }

    // استرجاع نوع الجهاز
    const deviceSelector = document.getElementById('device-selector');
    if (deviceSelector && deviceType) {
        deviceSelector.value = deviceType;

        // تطبيق إعدادات الجهاز
        setTimeout(() => {
            changeDeviceView(deviceType);

            // تمرير الإطار إلى العرض
            scrollDeviceFrameIntoView();
        }, 500);
    }
}

// دالة لضمان أن الإطار مرئي بالكامل
function scrollDeviceFrameIntoView() {
    setTimeout(() => {
        const deviceFrame = document.getElementById('device-frame');
        if (deviceFrame) {
            // استخدام scrollIntoView لضمان رؤية الإطار بالكامل
            deviceFrame.scrollIntoView({
                block: 'center',
                inline: 'center',
                behavior: 'smooth'
            });

            // تأكيد إضافي من عدم تغطية الجزء العلوي
            const container = document.getElementById('preview-container');
            if (container) {
                container.scrollTop = 0;
            }
        }
    }, 300);
}



// Window resize event listener
window.addEventListener('resize', function() {
    // Update Monaco editor layout
    if (monacoEditor) {
        monacoEditor.layout();
    }

    // Update status bar responsiveness
    updateStatusBarResponsiveness();

    // Other window resize handlers...
});

// دوال لدعم ربط الملفات في HTML

// العثور على الملفات المرتبطة في HTML
function findLinkedFiles(htmlContent) {
    const linkedFiles = [];

    // البحث عن روابط CSS
    const cssRegex = /<link[^>]*href=["']([^"']+)["'][^>]*>/gi;
    let cssMatch;
    while ((cssMatch = cssRegex.exec(htmlContent)) !== null) {
        const href = cssMatch[1];
        if (href.endsWith('.css') || cssMatch[0].includes('stylesheet')) {
            linkedFiles.push({
                path: href,
                type: 'css'
            });
        }
    }

    // البحث عن روابط JavaScript
    const jsRegex = /<script[^>]*src=["']([^"']+)["'][^>]*>/gi;
    let jsMatch;
    while ((jsMatch = jsRegex.exec(htmlContent)) !== null) {
        const src = jsMatch[1];
        linkedFiles.push({
            path: src,
            type: 'js'
        });
    }

    // البحث عن روابط الصور
    const imgRegex = /<img[^>]*src=["']([^"']+)["'][^>]*>/gi;
    let imgMatch;
    while ((imgMatch = imgRegex.exec(htmlContent)) !== null) {
        const imgPath = imgMatch[1];
        // تجاهل الصور التي تبدأ بـ http:// أو https:// أو data:
        if (!imgPath.startsWith('http://') && !imgPath.startsWith('https://') && !imgPath.startsWith('data:')) {
            linkedFiles.push({
                path: imgPath,
                type: 'img'
            });
        }
    }

    return linkedFiles;
}

// البحث عن ملف حسب المسار
function findFileByPath(filePath) {
    // تنظيف المسار
    let normalizedPath = filePath.trim();

    // إذا كان المسار مطلقًا (يبدأ بـ /)، نزيل الـ / الأولى
    if (normalizedPath.startsWith('/')) {
        normalizedPath = normalizedPath.substring(1);
    }

    // البحث في جميع الملفات
    for (const fileId in workspace.files) {
        const file = workspace.files[fileId];

        // تحقق من تطابق الاسم مباشرة
        if (file.name === normalizedPath) {
            return file;
        }

        // تحقق من تطابق المسار الكامل
        const fullPath = file.path.startsWith('/') ? file.path.substring(1) : file.path;
        if (fullPath === normalizedPath || fullPath + file.name === normalizedPath) {
            return file;
        }

        // تحقق من تطابق الاسم فقط (للملفات في المجلد الحالي)
        if (normalizedPath.indexOf('/') === -1 && file.name === normalizedPath) {
            return file;
        }
    }

    return null;
}

// إنشاء خريطة الموارد (Blob URLs)
function createResourceMap(linkedFiles) {
    const resourceMap = {};

    for (const linkedFile of linkedFiles) {
        const file = findFileByPath(linkedFile.path);
        if (file) {
            // تحديد نوع المحتوى
            let mimeType = 'text/plain';
            if (linkedFile.type === 'css') {
                mimeType = 'text/css';
            } else if (linkedFile.type === 'js') {
                mimeType = 'application/javascript';
            } else if (linkedFile.type === 'img') {
                // تحديد نوع MIME للصورة بناءً على الامتداد
                const extension = linkedFile.path.split('.').pop().toLowerCase();
                switch (extension) {
                    case 'png':
                        mimeType = 'image/png';
                        break;
                    case 'jpg':
                    case 'jpeg':
                        mimeType = 'image/jpeg';
                        break;
                    case 'gif':
                        mimeType = 'image/gif';
                        break;
                    case 'svg':
                        mimeType = 'image/svg+xml';
                        break;
                    default:
                        mimeType = 'image/png';
                }
            }

            // إنشاء Blob URL
            const blob = new Blob([file.content], { type: mimeType });
            resourceMap[linkedFile.path] = URL.createObjectURL(blob);
        }
    }

    return resourceMap;
}

// استبدال روابط الموارد في HTML
function replaceLinkedResources(htmlContent, resourceMap) {
    let modifiedHTML = htmlContent;

    // استبدال روابط CSS
    for (const [path, url] of Object.entries(resourceMap)) {
        if (path.endsWith('.css')) {
            const regex = new RegExp(`href=["']${escapeRegExp(path)}["']`, 'gi');
            modifiedHTML = modifiedHTML.replace(regex, `href="${url}"`);
        }
    }

    // استبدال روابط JavaScript
    for (const [path, url] of Object.entries(resourceMap)) {
        if (path.endsWith('.js')) {
            const regex = new RegExp(`src=["']${escapeRegExp(path)}["']`, 'gi');
            modifiedHTML = modifiedHTML.replace(regex, `src="${url}"`);
        }
    }

    // استبدال روابط الصور
    for (const [path, url] of Object.entries(resourceMap)) {
        if (!path.endsWith('.css') && !path.endsWith('.js')) {
            const regex = new RegExp(`src=["']${escapeRegExp(path)}["']`, 'gi');
            modifiedHTML = modifiedHTML.replace(regex, `src="${url}"`);
        }
    }

    return modifiedHTML;
}

// دالة لمعالجة الأحرف الخاصة في التعبيرات النمطية
function escapeRegExp(string) {
    return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
}

// تهيئة التيرمنال عند تحميل الصفحة
function initializeTerminal() {
    // إضافة مقبض تغيير الحجم
    addTerminalResizer();

    // استعادة حالة التيرمنال
    restoreTerminalState();

    // تحديث رأس التيرمنال
    updateTerminalHeader();

    // إضافة زر التيرمنال في شريط الحالة
    addTerminalToggleButton();

    // إضافة مستمع لمفتاح ESC لإغلاق التيرمنال
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape' && document.getElementById('code-executor').classList.contains('visible')) {
            const executorFooter = document.querySelector('.executor-footer');
            if (executorFooter && !executorFooter.classList.contains('collapsed') && !executorFooter.classList.contains('hidden')) {
                executorFooter.classList.add('collapsed');
                localStorage.setItem('terminalState', 'collapsed');
                e.preventDefault();
            }
        }
    });
}

// تحديث تلميح الأداة لزر اختيار الجهاز
function updateDeviceButtonTooltip(deviceType) {
    const deviceNames = {
        responsive: 'تجاوب كامل',
        desktop: 'سطح المكتب',
        laptop: 'لابتوب',
        tablet: 'تابلت',
        mobile: 'موبايل',
        custom: 'مخصص'
    };

    const deviceIcon = document.querySelector('.device-select-wrapper i');
    if (deviceIcon) {
        // تغيير الرمز حسب نوع الجهاز
        deviceIcon.className = 'fas';
        if (deviceType === 'mobile') {
            deviceIcon.classList.add('fa-mobile-alt');
        } else if (deviceType === 'tablet') {
            deviceIcon.classList.add('fa-tablet-alt');
        } else if (deviceType === 'laptop') {
            deviceIcon.classList.add('fa-laptop');
        } else if (deviceType === 'desktop') {
            deviceIcon.classList.add('fa-desktop');
        } else if (deviceType === 'custom') {
            deviceIcon.classList.add('fa-ruler-combined');
        } else {
            deviceIcon.classList.add('fa-expand-arrows-alt');
        }

        // تحديث نص التلميح
        deviceIcon.setAttribute('title', `العرض: ${deviceNames[deviceType] || 'تجاوب كامل'}`);
    }
}

// دالة تهيئة مفتش المتصفح (DevTools)
function initInspector() {
    // الحصول على العناصر
    const sidebar = document.getElementById('web-preview-sidebar');
    const iframe = document.getElementById('web-preview-iframe');
    const overlay = document.getElementById('inspector-overlay');
    const highlighter = document.getElementById('element-highlighter');
    const elementInfo = document.getElementById('element-info');
    const elementPath = document.getElementById('element-path');
    const sizeInfo = document.getElementById('selected-element-size');
    const mousePosition = document.getElementById('mouse-position');
    const gridOverlay = document.getElementById('grid-overlay');

    if (!sidebar || !iframe || !overlay) return;

    // تفعيل وضع المفتش
    sidebar.classList.add('inspector-mode');

    // إضافة زر التبديل بين الوضعين إذا لم يكن موجودًا
    if (!document.getElementById('inspector-mode-toggle')) {
        const toggleBtn = document.createElement('button');
        toggleBtn.id = 'inspector-mode-toggle';
        toggleBtn.className = 'inspector-mode-toggle';
        toggleBtn.innerHTML = '<i class="fas fa-code"></i>';
        toggleBtn.title = 'تبديل وضع المفتش';
        toggleBtn.onclick = toggleInspectorMode;
        document.querySelector('.preview-container').appendChild(toggleBtn);
    }

    // تحديث أبعاد الجهاز
    updateDeviceDimensions();

    // أزرار شريط أدوات المفتش
    document.getElementById('inspector-cursor').addEventListener('click', () => setInspectorTool('cursor'));
    document.getElementById('inspector-inspect').addEventListener('click', () => setInspectorTool('inspect'));
    document.getElementById('inspector-responsive').addEventListener('click', () => setInspectorTool('responsive'));
    document.getElementById('inspector-ruler').addEventListener('click', () => setInspectorTool('ruler'));
    document.getElementById('inspector-toggle-grid').addEventListener('click', toggleGridOverlay);
    document.getElementById('inspector-toggle-outline').addEventListener('click', toggleElementsOutline);
    document.getElementById('rotate-device').addEventListener('click', rotateDevice);
    document.getElementById('device-zoom').addEventListener('change', changeDeviceZoom);
    document.getElementById('network-throttle').addEventListener('change', changeNetworkThrottle);

    // محاكاة المفتش - فحص العناصر
    let inspectMode = false;

    // دالة تفعيل أداة من أدوات المفتش
    function setInspectorTool(tool) {
        // إلغاء تنشيط جميع الأزرار
        document.querySelectorAll('.inspector-btn[data-active="true"]').forEach(btn => {
            btn.setAttribute('data-active', 'false');
        });

        // تنشيط الزر المختار
        document.getElementById(`inspector-${tool}`).setAttribute('data-active', 'true');

        // تنفيذ الإجراء المناسب للأداة
        switch (tool) {
            case 'cursor':
                disableInspectMode();
                break;
            case 'inspect':
                enableInspectMode();
                break;
            case 'responsive':
                enableResponsiveMode();
                break;
            case 'ruler':
                enableRulerMode();
                break;
        }
    }

    // تفعيل وضع الفحص
    function enableInspectMode() {
        inspectMode = true;
        overlay.style.display = 'block';
        iframe.style.pointerEvents = 'none';

        // إزالة مستمعي الأحداث السابقة إن وجدت
        document.querySelector('.preview-container').removeEventListener('mousemove', handleInspectMouseMove);
        document.querySelector('.preview-container').removeEventListener('click', handleInspectClick);

        // إضافة مستمعي الأحداث
        document.querySelector('.preview-container').addEventListener('mousemove', handleInspectMouseMove);
        document.querySelector('.preview-container').addEventListener('click', handleInspectClick);
    }

    // إلغاء وضع الفحص
    function disableInspectMode() {
        inspectMode = false;
        overlay.style.display = 'none';
        iframe.style.pointerEvents = 'auto';
        highlighter.style.display = 'none';
        elementInfo.style.display = 'none';

        // إزالة مستمعي الأحداث
        document.querySelector('.preview-container').removeEventListener('mousemove', handleInspectMouseMove);
        document.querySelector('.preview-container').removeEventListener('click', handleInspectClick);

        // إعادة تعيين معلومات العنصر
        elementPath.textContent = '';
        sizeInfo.textContent = '';
    }

    // معالجة حدث تحريك الماوس في وضع الفحص
    function handleInspectMouseMove(e) {
        if (!inspectMode || !iframe.contentDocument) return;

        // حساب الموضع داخل الإطار
        const rect = iframe.getBoundingClientRect();
        const scale = parseFloat(iframe.style.transform?.match(/scale\(([^)]+)\)/)?.[1] || 1);

        // حساب إحداثيات الماوس داخل الإطار مع مراعاة المقياس
        const x = (e.clientX - rect.left) / scale;
        const y = (e.clientY - rect.top) / scale;

        // تحديث موضع الماوس في شريط المعلومات
        mousePosition.textContent = `${Math.round(x)}px × ${Math.round(y)}px`;

        // الحصول على العنصر تحت الماوس
        const element = getElementFromPoint(x, y);
        if (element) {
            highlightElement(element, x, y);
        }
    }

    // معالجة حدث النقر في وضع الفحص
    function handleInspectClick(e) {
        if (!inspectMode || !iframe.contentDocument) return;
        e.preventDefault();

        // حساب الموضع داخل الإطار
        const rect = iframe.getBoundingClientRect();
        const scale = parseFloat(iframe.style.transform?.match(/scale\(([^)]+)\)/)?.[1] || 1);
        const x = (e.clientX - rect.left) / scale;
        const y = (e.clientY - rect.top) / scale;

        // الحصول على العنصر تحت الماوس
        const element = getElementFromPoint(x, y);
        if (element) {
            selectElement(element);
        }
    }

    // الحصول على العنصر عند نقطة معينة داخل الإطار
    function getElementFromPoint(x, y) {
        try {
            if (!iframe.contentDocument) return null;
            return iframe.contentDocument.elementFromPoint(x, y);
        } catch (e) {
            console.error('خطأ في الحصول على العنصر:', e);
            return null;
        }
    }

    // تظليل العنصر
    function highlightElement(element, x, y) {
        if (!element || element.nodeType !== 1) return;

        try {
            const rect = element.getBoundingClientRect();

            // ضبط موضع وحجم المظلل
            highlighter.style.display = 'block';
            highlighter.style.left = `${rect.left}px`;
            highlighter.style.top = `${rect.top}px`;
            highlighter.style.width = `${rect.width}px`;
            highlighter.style.height = `${rect.height}px`;

            // عرض معلومات العنصر
            elementInfo.style.display = 'block';
            elementInfo.style.left = `${x + 10}px`;
            elementInfo.style.top = `${y + 10}px`;

            // إعداد محتوى معلومات العنصر
            const tagName = element.tagName.toLowerCase();
            const classes = Array.from(element.classList).join('.');
            const id = element.id ? `#${element.id}` : '';

            // تنسيق العرض
            elementInfo.textContent = classes.length > 0 ?
                `${tagName}${id}.${classes}` :
                `${tagName}${id}`;

            // تحديث مسار العنصر في شريط المعلومات
            elementPath.textContent = getElementPath(element);

            // عرض أبعاد العنصر
            sizeInfo.textContent = `${Math.round(rect.width)} × ${Math.round(rect.height)}`;
        } catch (e) {
            console.error('خطأ في تظليل العنصر:', e);
        }
    }

    // اختيار عنصر للفحص
    function selectElement(element) {
        // يمكن إضافة مزيد من المنطق هنا
        console.log('العنصر المحدد:', element);

        // تحديث شريط المعلومات
        updateElementInfo(element);
    }

    // تحديث معلومات العنصر المحدد
    function updateElementInfo(element) {
        if (!element || element.nodeType !== 1) return;

        try {
            // تحديث مسار العنصر
            elementPath.textContent = getElementPath(element);

            // تحديث معلومات الحجم
            const rect = element.getBoundingClientRect();
            sizeInfo.textContent = `${Math.round(rect.width)} × ${Math.round(rect.height)}`;

            // تمييز العنصر بحدود
            const originalOutline = element.style.outline;
            element.style.outline = '2px solid #6464ff';

            // إزالة التمييز بعد ثانيتين
            setTimeout(() => {
                element.style.outline = originalOutline;
            }, 2000);
        } catch (e) {
            console.error('خطأ في تحديث معلومات العنصر:', e);
        }
    }

    // الحصول على مسار العنصر في DOM
    function getElementPath(element, maxDepth = 3) {
        if (!element || element.nodeType !== 1) return '';

        let path = [];
        let current = element;
        let depth = 0;

        while (current && current.nodeType === 1 && depth < maxDepth) {
            let selector = current.tagName.toLowerCase();

            if (current.id) {
                selector += `#${current.id}`;
            } else if (current.className) {
                const classList = Array.from(current.classList);
                if (classList.length > 0) {
                    selector += `.${classList[0]}`;
                }
            }

            path.unshift(selector);
            current = current.parentElement;
            depth++;
        }

        if (current && current.nodeType === 1) {
            path.unshift('...');
        }

        return path.join(' > ');
    }

    // تفعيل وضع التجاوب
    function enableResponsiveMode() {
        // تعيين الجهاز إلى "تجاوب كامل"
        const deviceSelector = document.getElementById('device-selector');
        if (deviceSelector) {
            deviceSelector.value = 'responsive';
            changeDeviceView('responsive');
        }
    }

    // تفعيل وضع القياس
    function enableRulerMode() {
        // تنفيذ منطق وضع القياس هنا
        showInfo('وضع القياس غير متاح حاليًا، سيتم إضافته في التحديث القادم.', 'ميزة قادمة');
        setInspectorTool('cursor');
    }

    // تبديل إظهار شبكة التوجيه
    function toggleGridOverlay() {
        const button = document.getElementById('inspector-toggle-grid');
        const isActive = button.getAttribute('data-active') === 'true';

        button.setAttribute('data-active', !isActive);
        gridOverlay.style.display = isActive ? 'none' : 'block';
    }

    // تبديل إظهار حدود العناصر
    function toggleElementsOutline() {
        const button = document.getElementById('inspector-toggle-outline');
        const isActive = button.getAttribute('data-active') === 'true';

        button.setAttribute('data-active', !isActive);

        try {
            if (!iframe.contentDocument) return;

            if (isActive) {
                // إزالة الأنماط
                const style = iframe.contentDocument.getElementById('inspector-outline-style');
                if (style) style.remove();
            } else {
                // إضافة أنماط لإظهار حدود العناصر
                const style = document.createElement('style');
                style.id = 'inspector-outline-style';
                style.textContent = `
                    * {
                        outline: 1px dashed rgba(120, 120, 180, 0.4) !important;
                    }

                    *:hover {
                        outline: 1px dashed rgba(120, 120, 180, 0.8) !important;
                    }
                `;
                iframe.contentDocument.head.appendChild(style);
            }
        } catch (e) {
            console.error('خطأ في تبديل حدود العناصر:', e);
        }
    }

    // تدوير الجهاز
    function rotateDevice() {
        const deviceType = document.getElementById('device-selector').value;

        // لا نقوم بالتدوير في الوضع المتجاوب
        if (deviceType === 'responsive') return;

        // استبدال العرض بالارتفاع والعكس في المحاكي
        const deviceFrame = document.getElementById('device-frame');
        const tempWidth = deviceFrame.style.width;
        deviceFrame.style.width = deviceFrame.style.height;
        deviceFrame.style.height = tempWidth;

        // تحديث الأبعاد
        updateDeviceDimensions();

        // أعد حساب المقياس
        const containerWidth = document.querySelector('.preview-container').clientWidth - 80;
        const containerHeight = document.querySelector('.preview-container').clientHeight - 100;
        const deviceWidth = deviceFrame.offsetWidth;
        const deviceHeight = deviceFrame.offsetHeight;

        let scale = Math.min(
            containerWidth / deviceWidth,
            containerHeight / deviceHeight
        );

        scale = Math.min(Math.max(0.2, scale), 1);

        deviceFrame.style.transform = `scale(${scale})`;
    }

    // تغيير تكبير الجهاز
    function changeDeviceZoom(e) {
        const zoom = parseFloat(e.target.value);
        const deviceFrame = document.getElementById('device-frame');

        deviceFrame.style.transform = `scale(${zoom})`;
    }

    // تغيير محاكاة سرعة الشبكة
    function changeNetworkThrottle(e) {
        const throttle = e.target.value;
        console.log(`تم تغيير محاكاة الشبكة إلى: ${throttle}`);

        // يمكن إضافة منطق محاكاة السرعة هنا
        const networkIcon = document.querySelector('.network-select i');

        switch (throttle) {
            case 'online':
                networkIcon.className = 'fas fa-wifi';
                break;
            case 'fast3g':
                networkIcon.className = 'fas fa-signal';
                break;
            case 'slow3g':
                networkIcon.className = 'fas fa-signal';
                networkIcon.style.opacity = '0.6';
                break;
            case 'offline':
                networkIcon.className = 'fas fa-ban';
                break;
        }
    }

    // تحديث أبعاد الجهاز في العرض (دالة عامة)
    function updateDeviceDimensions() {
        const deviceFrame = document.getElementById('device-frame');
        const widthSpan = document.getElementById('device-width');
        const heightSpan = document.getElementById('device-height');
        if (deviceFrame && widthSpan && heightSpan) {
            // استخدام offsetWidth و offsetHeight لأنها تتضمن الحدود
            const width = deviceFrame.offsetWidth;
            const height = deviceFrame.offsetHeight;
            widthSpan.textContent = Math.round(width);
            heightSpan.textContent = Math.round(height);
        }
    }
}

// دالة تبديل وضع المفتش
function toggleInspectorMode() {
    const sidebar = document.getElementById('web-preview-sidebar');
    if (!sidebar) return;

    // تبديل حالة وضع المفتش
    if (sidebar.classList.contains('inspector-mode')) {
        sidebar.classList.remove('inspector-mode');
        document.getElementById('inspector-overlay').style.display = 'none';
    } else {
        sidebar.classList.add('inspector-mode');
        initInspector();
    }
}

// دالة إظهار/إخفاء مفتش المتصفح في معاينة الويب
function toggleWebInspector() {
    const sidebar = document.getElementById('web-preview-sidebar');
    if (!sidebar) return;

    // تبديل وضع المفتش
    if (sidebar.classList.contains('inspector-mode')) {
        sidebar.classList.remove('inspector-mode');
        document.getElementById('inspector-overlay').style.display = 'none';

        // حفظ الإعداد
        localStorage.setItem('inspectorModeActive', 'false');
    } else {
        sidebar.classList.add('inspector-mode');
        initInspector();

        // حفظ الإعداد
        localStorage.setItem('inspectorModeActive', 'true');
    }
}

// دالة لإضافة زر المفتش إلى شريط أدوات المعاينة
function addInspectorButton() {
    const previewControls = document.querySelector('.preview-controls');
    if (!previewControls) return;

    // تحقق من عدم وجود الزر مسبقًا
    if (document.getElementById('preview-inspector')) return;

    // إنشاء زر المفتش
    const inspectorBtn = document.createElement('button');
    inspectorBtn.id = 'preview-inspector';
    inspectorBtn.className = 'preview-btn';
    inspectorBtn.title = 'مفتش العناصر';
    inspectorBtn.innerHTML = '<i class="fas fa-search"></i>';
    inspectorBtn.onclick = toggleWebInspector;

    // إضافة الزر قبل زر الإغلاق
    const closeBtn = document.getElementById('preview-close');
    if (closeBtn) {
        previewControls.insertBefore(inspectorBtn, closeBtn);
    } else {
        previewControls.appendChild(inspectorBtn);
    }
}

// إضافة مستمع حدث DOMContentLoaded لتهيئة واجهة المفتش
document.addEventListener('DOMContentLoaded', function() {
    // إضافة زر المفتش إذا كانت نافذة المعاينة مفتوحة
    setTimeout(() => {
        const previewSidebar = document.getElementById('web-preview-sidebar');
        if (previewSidebar && previewSidebar.style.display !== 'none') {
            addInspectorButton();
        }

        // استعادة وضع المفتش إذا كان مفعلاً
        const inspectorMode = localStorage.getItem('inspectorModeActive') === 'true';
        if (inspectorMode && previewSidebar) {
            previewSidebar.classList.add('inspector-mode');
            initInspector();
        }
    }, 1000); // انتظر لتأكد من تحميل جميع العناصر
});

// إنشاء تيرمنال جديد
function createNewTerminal() {
    // حفظ محتوى التيرمنال الحالي
    saveTerminalContent();

    // إنشاء تيرمنال جديد
    const terminalElem = document.getElementById('executor-result');
    if (terminalElem) {
        terminalElem.innerHTML = '<div class="terminal-welcome">New terminal session started.</div>';
    }

    // تنشيط تبويب Terminal
    const terminalTab = document.querySelector('.terminal-tab[data-tab="terminal"]');
    if (terminalTab) {
        const allTabs = document.querySelectorAll('.terminal-tab');
        allTabs.forEach(tab => tab.classList.remove('active'));
        terminalTab.classList.add('active');
    }
}

// تقسيم التيرمنال
function splitTerminal() {
    const executorFooter = document.querySelector('.executor-footer');
    if (!executorFooter) return;

    // تحقق إذا كان التيرمنال مقسم بالفعل
    if (executorFooter.classList.contains('split')) {
        // إذا كان مقسم بالفعل، نعيده إلى الوضع العادي
        executorFooter.classList.remove('split');

        // إزالة التيرمنال الثاني
        const secondTerminal = document.getElementById('second-terminal');
        if (secondTerminal) {
            secondTerminal.remove();
        }
    } else {
        // إضافة صنف split للتيرمنال
        executorFooter.classList.add('split');

        // إنشاء تيرمنال ثاني
        const terminalContainer = document.createElement('div');
        terminalContainer.className = 'terminal second-terminal';
        terminalContainer.id = 'second-terminal';
        terminalContainer.innerHTML = '<div class="terminal-welcome">Split terminal ready.</div>';

        // إضافة التيرمنال الثاني بعد التيرمنال الأول
        const firstTerminal = document.getElementById('executor-result');
        if (firstTerminal && firstTerminal.parentNode) {
            firstTerminal.parentNode.insertBefore(terminalContainer, firstTerminal.nextSibling);
        }
    }
}

// عرض خيارات إضافية للتيرمنال
function showTerminalOptions(event) {
    // إيقاف انتشار الحدث لمنع إغلاق القائمة فورًا
    event.stopPropagation();

    // التحقق مما إذا كانت القائمة موجودة بالفعل
    let optionsMenu = document.getElementById('terminal-options-menu');

    if (optionsMenu) {
        // إذا كانت القائمة مفتوحة بالفعل، نغلقها
        optionsMenu.remove();
        return;
    }

    // إنشاء قائمة الخيارات
    optionsMenu = document.createElement('div');
    optionsMenu.id = 'terminal-options-menu';
    optionsMenu.className = 'terminal-options-menu';

    // إضافة خيارات القائمة
    optionsMenu.innerHTML = `
        <div class="terminal-option" onclick="changeTerminalFont()">Change Font Size</div>
        <div class="terminal-option" onclick="changeTerminalTheme()">Change Terminal Theme</div>
        <div class="terminal-option" onclick="clearTerminalHistory()">Clear Terminal History</div>
        <div class="terminal-option" onclick="configureTerminal()">Terminal Settings</div>
    `;

    // تحديد موضع القائمة بالنسبة للزر
    const button = event.currentTarget;
    const buttonRect = button.getBoundingClientRect();

    // إضافة القائمة إلى DOM
    document.body.appendChild(optionsMenu);

    // تحديد موضع القائمة
    optionsMenu.style.position = 'absolute';
    optionsMenu.style.top = `${buttonRect.bottom}px`;
    optionsMenu.style.right = `${window.innerWidth - buttonRect.right}px`;

    // إضافة مستمع حدث لإغلاق القائمة عند النقر في أي مكان آخر
    setTimeout(() => {
        document.addEventListener('click', closeTerminalOptions);
    }, 10);
}

// إغلاق قائمة خيارات التيرمنال
function closeTerminalOptions() {
    const optionsMenu = document.getElementById('terminal-options-menu');
    if (optionsMenu) {
        optionsMenu.remove();
    }
    document.removeEventListener('click', closeTerminalOptions);
}

// وظائف قائمة الخيارات
function changeTerminalFont() {
    const fontSize = prompt('Enter terminal font size (px):', '13');
    if (fontSize) {
        const terminal = document.getElementById('executor-result');
        if (terminal) {
            terminal.style.fontSize = `${fontSize}px`;
            localStorage.setItem('terminalFontSize', fontSize);
        }
    }
    closeTerminalOptions();
}

function changeTerminalTheme() {
    const themes = ['Dark (Default)', 'Light', 'Blue', 'Green', 'Amber'];
    const theme = prompt(`Select terminal theme (0-${themes.length - 1}):\n${themes.map((t, i) => `${i}: ${t}`).join('\n')}`, '0');

    if (theme !== null) {
        const themeIndex = parseInt(theme);
        if (!isNaN(themeIndex) && themeIndex >= 0 && themeIndex < themes.length) {
            const terminal = document.getElementById('executor-result');
            if (terminal) {
                // إزالة جميع أصناف السمات السابقة
                terminal.classList.remove('theme-dark', 'theme-light', 'theme-blue', 'theme-green', 'theme-amber');

                // إضافة صنف السمة الجديدة
                const themeClass = `theme-${themes[themeIndex].toLowerCase().split(' ')[0]}`;
                terminal.classList.add(themeClass);
                localStorage.setItem('terminalTheme', themeClass);
            }
        }
    }
    closeTerminalOptions();
}

async function clearTerminalHistory() {
    const confirmed = await showConfirm(
        'هل أنت متأكد من مسح جميع سجلات التيرمنال؟ لا يمكن التراجع عن هذا الإجراء.',
        'مسح سجل التيرمنال'
    );

    if (confirmed) {

        localStorage.removeItem('terminalContent');

        const terminalElem = document.getElementById('executor-result');
        if (terminalElem) {
            terminalElem.innerHTML = '<div class="terminal-welcome">Terminal history cleared.</div>';
        }

        // إظهار إشعار نجاح
        showSuccess('تم مسح سجل التيرمنال بنجاح!', 'مسح السجل');
    }
    closeTerminalOptions();
}

function configureTerminal() {
    showInfo('إعدادات التيرمنال ستكون متاحة في التحديث القادم.', 'ميزة قادمة');
    closeTerminalOptions();
}



// تقسيم المحرر
function splitEditor() {
    showInfo('ميزة تقسيم المحرر ستكون متاحة في التحديث القادم.', 'ميزة قادمة');
}

// عرض خيارات المحرر
function showEditorOptions(event) {
    event.stopPropagation();

    let optionsMenu = document.getElementById('editor-options-menu');

    if (optionsMenu) {
        optionsMenu.remove();
        return;
    }

    optionsMenu = document.createElement('div');
    optionsMenu.id = 'editor-options-menu';
    optionsMenu.className = 'editor-options-menu';

    optionsMenu.innerHTML = `
        <div class="editor-option" onclick="changeEditorTheme()">Change Theme</div>
        <div class="editor-option" onclick="changeFontSize()">Change Font Size</div>
        <div class="editor-option" onclick="toggleMinimap()">Toggle Minimap</div>
        <div class="editor-option" onclick="formatDocument()">Format Document</div>
    `;

    const button = event.currentTarget;
    const buttonRect = button.getBoundingClientRect();

    document.body.appendChild(optionsMenu);

    optionsMenu.style.position = 'absolute';
    optionsMenu.style.top = `${buttonRect.bottom}px`;
    optionsMenu.style.right = `${window.innerWidth - buttonRect.right}px`;

    setTimeout(() => {
        document.addEventListener('click', closeEditorOptions);
    }, 10);
}

function closeEditorOptions() {
    const optionsMenu = document.getElementById('editor-options-menu');
    if (optionsMenu) {
        optionsMenu.remove();
    }
    document.removeEventListener('click', closeEditorOptions);
}

// وظائف خيارات المحرر
function changeEditorTheme() {
    const themes = ['vs-dark', 'vs', 'hc-black'];
    const theme = prompt(`Select editor theme (0-${themes.length - 1}):\n0: Dark\n1: Light\n2: High Contrast`, '0');

    if (theme !== null && monacoEditor) {
        const themeIndex = parseInt(theme);
        if (!isNaN(themeIndex) && themeIndex >= 0 && themeIndex < themes.length) {
            monaco.editor.setTheme(themes[themeIndex]);
            localStorage.setItem('editorTheme', themes[themeIndex]);
        }
    }
    closeEditorOptions();
}

function changeFontSize() {
    const fontSize = prompt('Enter editor font size (px):', '14');
    if (fontSize && monacoEditor) {
        const size = parseInt(fontSize);
        if (!isNaN(size) && size > 0) {
            monacoEditor.updateOptions({ fontSize: size });
            localStorage.setItem('editorFontSize', size);
        }
    }
    closeEditorOptions();
}

function toggleMinimap() {
    if (monacoEditor) {
        const currentState = monacoEditor.getOption(monaco.editor.EditorOption.minimap).enabled;
        monacoEditor.updateOptions({ minimap: { enabled: !currentState } });
        localStorage.setItem('editorMinimapEnabled', !currentState);
    }
    closeEditorOptions();
}

function formatDocument() {
    if (monacoEditor) {
        monacoEditor.getAction('editor.action.formatDocument').run();
    }
    closeEditorOptions();
}

// تحديث شريط الحالة
function updateStatusBar(file) {
    const statusBar = document.querySelector('.status-bar');
    if (!statusBar) return;

    const languageIndicator = statusBar.querySelector('.language-indicator .status-item-text');
    if (languageIndicator && file) {
        languageIndicator.textContent = file.language.charAt(0).toUpperCase() + file.language.slice(1);
    }
}

// دالة تصغير نافذة المعاينة
function minimizeWebPreview() {
    const sidebar = document.getElementById('web-preview-sidebar');
    if (sidebar) {
        sidebar.classList.toggle('minimized');
        sidebar.classList.remove('maximized');

        // حفظ الحالة
        localStorage.setItem('webPreviewMinimized', sidebar.classList.contains('minimized'));
        localStorage.setItem('webPreviewMaximized', false);
    }
}

// دالة تكبير نافذة المعاينة
function maximizeWebPreview() {
    const sidebar = document.getElementById('web-preview-sidebar');
    if (sidebar) {
        // حفظ الموضع والحجم الحالي قبل التكبير إذا لم تكن مكبرة بالفعل
        if (!sidebar.classList.contains('maximized')) {
            localStorage.setItem('webPreviewLastPosition', JSON.stringify({
                top: sidebar.style.top,
                left: sidebar.style.left,
                width: sidebar.style.width,
                height: sidebar.style.height
            }));
        }

        sidebar.classList.toggle('maximized');
        sidebar.classList.remove('minimized');

        // حفظ الحالة
        localStorage.setItem('webPreviewMaximized', sidebar.classList.contains('maximized'));
        localStorage.setItem('webPreviewMinimized', false);
    }
}

// دالة تغيير عرض الجهاز
function changeDeviceView(event) {
    const deviceType = event.target ? event.target.value : event;
    const deviceFrame = document.getElementById('device-frame');
    const container = document.getElementById('preview-container');
    const deviceWrapper = document.querySelector('.device-wrapper');
    const iframe = document.getElementById('web-preview-iframe');

    if (!deviceFrame || !container || !iframe) return;

    // إزالة جميع الفئات السابقة
    deviceFrame.className = 'device-frame';
    deviceFrame.style.width = '';
    deviceFrame.style.height = '';
    deviceFrame.style.transform = '';
    deviceFrame.style.border = 'none';
    deviceFrame.style.margin = '20px auto';
    deviceFrame.style.position = 'relative';
    container.style.overflow = 'auto';

    // تعريف إعدادات كل جهاز
    const deviceSettings = {
        responsive: { width: '100%', height: '100%', scale: 1, userAgent: null },
        desktop: { width: '1920px', height: '1080px', scale: 0.5, userAgent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64)" },
        laptop: { width: '1366px', height: '768px', scale: 0.7, userAgent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64)" },
        tablet: { width: '768px', height: '1024px', scale: 0.8, userAgent: "Mozilla/5.0 (iPad; CPU OS 15_0 like Mac OS X)" },
        mobile: { width: '414px', height: '896px', scale: 1, userAgent: "Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X)" }
    };

    // تطبيق إعدادات الجهاز المحدد
    if (deviceType !== 'custom') {
        const settings = deviceSettings[deviceType] || deviceSettings.responsive;

        deviceFrame.classList.add(deviceType);

        if (deviceType === 'responsive') {
            // الوضع المتجاوب: ملء المساحة المتاحة
            deviceFrame.style.width = '100%';
            deviceFrame.style.height = '100%';
            deviceFrame.style.margin = '0';
            container.style.overflow = 'hidden';

            // إعادة ضبط أي تحويلات
            deviceFrame.style.transform = 'none';

            if (deviceWrapper) {
                deviceWrapper.style.padding = '0';
                deviceWrapper.style.overflow = 'visible';
            }
        } else {
            // تطبيق الأبعاد المحددة
            deviceFrame.style.width = settings.width;
            deviceFrame.style.height = settings.height;

            // تطبيق حدود وإطارات خاصة بالجهاز
            if (deviceType === 'mobile') {
                deviceFrame.style.borderRadius = '20px';
                deviceFrame.style.border = '5px solid #333';
            } else if (deviceType === 'tablet') {
                deviceFrame.style.borderRadius = '12px';
                deviceFrame.style.border = '5px solid #333';
            } else if (deviceType === 'laptop') {
                deviceFrame.style.borderRadius = '6px 6px 0 0';
                deviceFrame.style.border = '5px solid #333';
                deviceFrame.style.borderBottom = '40px solid #333';
            } else if (deviceType === 'desktop') {
                deviceFrame.style.borderRadius = '6px 6px 0 0';
                deviceFrame.style.border = '5px solid #1e1e1e';
                deviceFrame.style.borderBottom = '25px solid #1e1e1e';
            }

            // حساب الأبعاد الحقيقية للعنصر متضمنة الحدود والحشوات
            const frameTotalWidth = deviceFrame.offsetWidth;
            const frameTotalHeight = deviceFrame.offsetHeight;

            // حساب مساحة العرض المتاحة مع هوامش أمان
            const containerAvailWidth = container.clientWidth - 40;
            const containerAvailHeight = container.clientHeight - 40;

            // احتساب المقياس بشكل أكثر دقة مع مراعاة جميع الأبعاد
            let scale = Math.min(
                containerAvailWidth / frameTotalWidth,
                containerAvailHeight / frameTotalHeight
            );

            // حد أدنى وأقصى للتكبير/التصغير
            scale = Math.min(Math.max(0.2, scale), 1);

            // تطبيق المقياس المناسب بناءً على حجم الشاشة
            const windowWidth = window.innerWidth;
            if (windowWidth <= 576) {
                // الأجهزة الصغيرة جدًا
                if (deviceType === 'mobile') {
                    scale = Math.min(scale, 0.9);
                } else {
                    scale = Math.min(scale, 0.5);
                }
            } else if (windowWidth <= 992) {
                // الأجهزة المتوسطة
                if (deviceType === 'mobile') {
                    scale = Math.min(scale, 1);
                } else if (deviceType === 'tablet') {
                    scale = Math.min(scale, 0.7);
                } else {
                    scale = Math.min(scale, 0.5);
                }
            } else if (windowWidth <= 1400) {
                // الأجهزة الكبيرة
                if (deviceType === 'mobile') {
                    scale = Math.min(scale, 1);
                } else if (deviceType === 'tablet') {
                    scale = Math.min(scale, 0.8);
                } else if (deviceType === 'laptop') {
                    scale = Math.min(scale, 0.6);
                } else {
                    scale = Math.min(scale, 0.5);
                }
            }

            // تطبيق التحويل مع مركزة العنصر
            deviceFrame.style.transform = `scale(${scale})`;
            deviceFrame.style.transformOrigin = 'center center';

            // ضمان عرض العنصر في المركز والمساحة المناسبة
            if (deviceWrapper) {
                deviceWrapper.style.display = 'flex';
                deviceWrapper.style.justifyContent = 'center';
                deviceWrapper.style.alignItems = 'center';
                deviceWrapper.style.padding = '20px';
                deviceWrapper.style.overflow = 'visible';
            }

            // زيادة الحشوة في حاوية المعاينة لضمان ظهور الجهاز بالكامل
            container.style.padding = '20px';
        }

        // إضافة meta viewport ديناميكي لمحاكاة الجهاز
        try {
            // انتظار تحميل iframe
            setTimeout(() => {
                if (iframe.contentDocument) {
                    // البحث عن meta viewport الحالي
                    let viewport = iframe.contentDocument.querySelector('meta[name="viewport"]');

                    // إنشاء واحد جديد إذا لم يكن موجودًا
                    if (!viewport) {
                        viewport = document.createElement('meta');
                        viewport.name = 'viewport';
                        iframe.contentDocument.head.appendChild(viewport);
                    }

                    // تعيين المحتوى المناسب للجهاز
                    if (deviceType === 'mobile') {
                        viewport.content = 'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover';
                    } else if (deviceType === 'tablet') {
                        viewport.content = 'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no';
                    } else if (deviceType === 'responsive') {
                        viewport.content = 'width=device-width, initial-scale=1.0';
                    } else {
                        viewport.content = 'width=device-width, initial-scale=1.0';
                    }

                    // محاكاة User-Agent للجهاز
                    if (settings.userAgent && iframe.contentWindow.navigator) {
                        try {
                            // بدلاً من محاولة تغيير user-agent بشكل مباشر، نكتفي بتسجيله في console
                            console.log('محاكاة User-Agent:', settings.userAgent);
                            // لا نحاول تعديل الخاصية لأنها read-only
                            // Object.defineProperty(iframe.contentWindow.navigator, 'userAgent', {
                            //     get: function() { return settings.userAgent; }
                            // });
                        } catch (e) {
                            console.warn('خطأ في محاكاة user-agent:', e);
                        }
                    }

                    // إضافة CSS لضبط المحتوى داخل iframe
                    if (iframe.contentDocument.head && !iframe.contentDocument.head.querySelector('style[data-device-style]')) {
                        const styleElement = document.createElement('style');
                        styleElement.setAttribute('data-device-style', 'true');
                        styleElement.textContent = `
                            html, body {
                                margin: 0;
                                padding: 0;
                                width: 100%;
                                height: 100%;
                                overflow: auto;
                            }
                            /* للتأكد من تناسب المحتوى مع إطار الجهاز */
                            * {
                                box-sizing: border-box;
                            }
                        `;
                        iframe.contentDocument.head.appendChild(styleElement);
                    }

                    // ضبط نمط body للتأكد من عدم وجود هوامش داخلية
                    if (iframe.contentDocument.body) {
                        iframe.contentDocument.body.className = `device-${deviceType}`;
                        iframe.contentDocument.body.style.margin = '0';
                        iframe.contentDocument.body.style.padding = '0';
                        iframe.contentDocument.body.style.width = '100%';
                        iframe.contentDocument.body.style.height = '100%';
                        iframe.contentDocument.body.style.overflow = 'auto';
                    }
                }
            }, 300);
        } catch (e) {
            console.warn('خطأ في ضبط meta viewport:', e);
        }
    } else {
        // إذا كان مخصصًا، اطلب الأبعاد
        const width = prompt('أدخل العرض بالبكسل:', '1024');
        const height = prompt('أدخل الارتفاع بالبكسل:', '768');

        if (width && height) {
            deviceFrame.style.width = `${width}px`;
            deviceFrame.style.height = `${height}px`;

            // ضبط التكبير/التصغير للأبعاد المخصصة
            // احتساب الأبعاد الحقيقية للعنصر
            const frameTotalWidth = parseInt(width) + 20; // هامش 10px من كل جانب
            const frameTotalHeight = parseInt(height) + 20; // هامش 10px من أعلى وأسفل

            // حساب مساحة العرض المتاحة مع هوامش أمان
            const containerAvailWidth = container.clientWidth - 80;
            const containerAvailHeight = container.clientHeight - 100;

            let scale = Math.min(
                containerAvailWidth / frameTotalWidth,
                containerAvailHeight / frameTotalHeight
            );

            // حد أدنى وأقصى للتكبير/التصغير
            scale = Math.min(Math.max(0.2, scale), 1);

            if (scale < 1) {
                // تطبيق التحويل مع مركزة العنصر
                deviceFrame.style.transform = `scale(${scale})`;
                deviceFrame.style.transformOrigin = 'center center';
                deviceFrame.style.position = 'relative';
                deviceFrame.style.margin = '40px auto';
                deviceFrame.style.top = '0';

                // ضمان عرض العنصر في المركز
                if (deviceWrapper) {
                    deviceWrapper.style.display = 'flex';
                    deviceWrapper.style.justifyContent = 'center';
                    deviceWrapper.style.alignItems = 'center';
                    deviceWrapper.style.padding = '40px 20px';
                    deviceWrapper.style.overflow = 'visible';
                }

                // زيادة الحشوة في حاوية المعاينة لضمان ظهور الجهاز بالكامل
                container.style.padding = '40px 20px';
            }

            // إضافة meta viewport للإعدادات المخصصة
            try {
                setTimeout(() => {
                    if (iframe.contentDocument) {
                        // تصحيح موضع iframe لضمان ظهوره بالكامل
                        iframe.style.position = 'relative';
                        iframe.style.top = '0';

                        let viewport = iframe.contentDocument.querySelector('meta[name="viewport"]');
                        if (!viewport) {
                            viewport = document.createElement('meta');
                            viewport.name = 'viewport';
                            iframe.contentDocument.head.appendChild(viewport);
                        }
                        viewport.content = `width=${width}, initial-scale=1.0`;

                        // إضافة class مخصص للـ body وضبط الأنماط
                        if (iframe.contentDocument.body) {
                            iframe.contentDocument.body.className = 'device-custom';
                            iframe.contentDocument.body.style.width = `${width}px`;
                            iframe.contentDocument.body.style.height = `${height}px`;
                            iframe.contentDocument.body.style.margin = '0';
                            iframe.contentDocument.body.style.padding = '0';
                            iframe.contentDocument.body.style.overflow = 'auto';

                            // إضافة CSS لضبط المحتوى داخل iframe
                            const styleElement = document.createElement('style');
                            styleElement.textContent = `
                                html, body {
                                    margin: 0;
                                    padding: 0;
                                    width: 100%;
                                    height: 100%;
                                    overflow: auto;
                                }
                                /* للتأكد من تناسب المحتوى مع الإطار المخصص */
                                .device-custom * {
                                    box-sizing: border-box;
                                }
                            `;
                            if (!iframe.contentDocument.head.querySelector('style[data-device-style]')) {
                                styleElement.setAttribute('data-device-style', 'true');
                                iframe.contentDocument.head.appendChild(styleElement);
                            }
                        }
                    }
                }, 500);
            } catch (e) {
                console.warn('خطأ في ضبط meta viewport للإعدادات المخصصة:', e);
            }
        }
    }

    // حفظ الإعداد المحدد
    localStorage.setItem('webPreviewDeviceType', deviceType);

    // تحديث نص تلميح الأداة للزر
    updateDeviceButtonTooltip(deviceType);

    // تحديث أبعاد الجهاز في واجهة المستخدم
    updateDeviceDimensions();
}

// ===== دوال معالجة مخططات Mermaid =====

// التحقق من كود Mermaid بطرق متعددة
function isMermaidCode(lang, content) {
    // تنظيف اللغة والمحتوى
    const cleanLang = (lang || '').toLowerCase().trim();
    const cleanContent = content.trim().toLowerCase();

    // 1. التحقق من اللغة المحددة صراحة
    const mermaidLanguages = [
        'mermaid',
        'mmd',
        'graph',
        'graphtd',
        'graph-td',
        'graph_td',
        'graphlr',
        'graph-lr',
        'graph_lr',
        'flowchart',
        'flow',
        'sequence',
        'sequencediagram',
        'sequence-diagram',
        'sequence_diagram',
        'gantt',
        'pie',
        'gitgraph',
        'git-graph',
        'git_graph',
        'erdiagram',
        'er-diagram',
        'er_diagram',
        'journey',
        'classDiagram',
        'class-diagram',
        'class_diagram',
        'stateDiagram',
        'state-diagram',
        'state_diagram',
        'diagram',
        'chart'
    ];

    if (mermaidLanguages.includes(cleanLang)) {
        return true;
    }

    // 2. التحقق من المحتوى نفسه - الكلمات المفتاحية لبداية مخططات Mermaid
    const mermaidKeywords = [
        'graph td', 'graph lr', 'graph bt', 'graph rl', 'graph tb',
        'flowchart td', 'flowchart lr', 'flowchart bt', 'flowchart rl', 'flowchart tb',
        'sequencediagram', 'sequence diagram',
        'gantt', 'pie title', 'pie chart', 'pie',
        'gitgraph', 'git graph',
        'erdiagram', 'er diagram',
        'journey', 'user journey',
        'classdiagram', 'class diagram',
        'statediagram', 'state diagram', 'statediagram-v2',
        'mindmap', 'mind map',
        'timeline'
    ];

    // التحقق من بداية المحتوى
    for (const keyword of mermaidKeywords) {
        if (cleanContent.startsWith(keyword)) {
            console.log(`🎯 تم اكتشاف مخطط Mermaid بالكلمة المفتاحية: "${keyword}"`);
            return true;
        }
    }

    // 3. التحقق من أنماط Mermaid الشائعة في المحتوى
    const mermaidPatterns = [
        // أنماط بداية المخططات
        { pattern: /^graph\s+(td|lr|bt|rl|tb)/i, name: 'Graph diagram', weight: 3 },
        { pattern: /^flowchart\s+(td|lr|bt|rl|tb)/i, name: 'Flowchart diagram', weight: 3 },
        { pattern: /^sequencediagram/i, name: 'Sequence diagram', weight: 3 },
        { pattern: /^gantt/i, name: 'Gantt chart', weight: 3 },
        { pattern: /^pie(\s+title)?/i, name: 'Pie chart', weight: 3 },
        { pattern: /^gitgraph/i, name: 'Git graph', weight: 3 },
        { pattern: /^erdiagram/i, name: 'ER diagram', weight: 3 },
        { pattern: /^classdiagram/i, name: 'Class diagram', weight: 3 },
        { pattern: /^statediagram/i, name: 'State diagram', weight: 3 },
        { pattern: /^journey/i, name: 'User journey', weight: 3 },
        { pattern: /^mindmap/i, name: 'Mind map', weight: 3 },
        { pattern: /^timeline/i, name: 'Timeline', weight: 3 },

        // أنماط العقد والأسهم
        { pattern: /\w+\s*-->\s*\w+/i, name: 'Arrow connections', weight: 2 },
        { pattern: /\w+\s*->>?\s*\w+/i, name: 'Sequence arrows', weight: 2 },
        { pattern: /\[\w+.*?\]/i, name: 'Square nodes', weight: 1 },
        { pattern: /\{\w+.*?\}/i, name: 'Diamond nodes', weight: 1 },
        { pattern: /\(\w+.*?\)/i, name: 'Round nodes', weight: 1 },
        { pattern: /\(\(\w+.*?\)\)/i, name: 'Circle nodes', weight: 2 },

        // كلمات مفتاحية خاصة
        { pattern: /participant\s+\w+/i, name: 'Participants', weight: 2 },
        { pattern: /activate\s+\w+/i, name: 'Activation', weight: 2 },
        { pattern: /note\s+(left|right|over)/i, name: 'Notes', weight: 2 },
        { pattern: /alt\s+/i, name: 'Alternative', weight: 2 },
        { pattern: /loop\s+/i, name: 'Loop', weight: 2 },
        { pattern: /section\s+/i, name: 'Section', weight: 2 },
        { pattern: /dateformat\s+/i, name: 'Date format', weight: 2 },
        { pattern: /subgraph\s+/i, name: 'Subgraph', weight: 2 },
        { pattern: /classDef\s+/i, name: 'Class definition', weight: 2 },
        { pattern: /:\s*\d+\.?\d*/i, name: 'Pie values', weight: 1 }
    ];

    let totalScore = 0;
    let foundPatterns = [];

    for (const item of mermaidPatterns) {
        const matches = content.match(item.pattern);
        if (matches) {
            const score = matches.length * item.weight;
            totalScore += score;
            foundPatterns.push(`${item.name} (${matches.length}x)`);

            // إذا وصلنا لنقاط كافية، فهو Mermaid
            if (totalScore >= 3) {
                console.log(`🎯 تم اكتشاف مخطط Mermaid بالأنماط: ${foundPatterns.join(', ')} - النقاط: ${totalScore}`);
                return true;
            }
        }
    }

    // 4. التحقق من اللغات العامة التي قد تحتوي على كود Mermaid
    const genericLanguages = ['plaintext', 'text', 'txt', 'plain', 'raw', '', null, undefined];
    if (genericLanguages.includes(cleanLang)) {
        // فحص أعمق للمحتوى
        const hasArrows = /-->|->|\->>|-->>|\|\|--\|\||\}--\{/.test(content);
        const hasNodes = /\[.*?\]|\{.*?\}|\(.*?\)|\(\(.*?\)\)|\[\[.*?\]\]/.test(content);
        const hasMermaidStructure = /graph|flowchart|sequencediagram|gantt|pie|participant|classDiagram|stateDiagram/i.test(content);
        const hasMermaidSyntax = /style\s+\w+\s+fill|click\s+\w+|subgraph/i.test(content);

        if ((hasArrows && hasNodes) || hasMermaidStructure || hasMermaidSyntax) {
            return true;
        }
    }

    // 5. فحص إضافي للمحتوى المشكوك فيه
    // إذا كان المحتوى يحتوي على كلمات مفتاحية قوية لـ Mermaid
    const strongMermaidIndicators = [
        /^graph\s+(td|lr|bt|rl)/im,
        /^flowchart\s+(td|lr|bt|rl)/im,
        /^sequencediagram/im,
        /participant\s+\w+\s+as/im,
        /\w+\s*->>?\+?\s*\w+\s*:/im,
        /^gantt$/im,
        /dateformat\s+/im,
        /^pie\s+title/im,
        /^classDiagram$/im,
        /^stateDiagram$/im
    ];

    for (const indicator of strongMermaidIndicators) {
        if (indicator.test(content)) {
            return true;
        }
    }

    // إذا وجدنا بعض الأنماط ولكن ليس كافياً، نسجل ذلك
    if (foundPatterns.length > 0) {
        console.log(`⚠️ أنماط محتملة لـ Mermaid: ${foundPatterns.join(', ')} - النقاط: ${totalScore} (غير كافية)`);
    } else {
        console.log(`❌ لم يتم اكتشاف أي أنماط Mermaid في المحتوى`);
    }

    return false;
}

// تهيئة Mermaid
function initializeMermaid() {
    if (typeof mermaid !== 'undefined') {
        mermaid.initialize({
            startOnLoad: false,
            theme: 'dark',
            themeVariables: {
                primaryColor: '#3b82f6',
                primaryTextColor: '#ffffff',
                primaryBorderColor: '#1d4ed8',
                lineColor: '#6b7280',
                sectionBkgColor: '#374151',
                altSectionBkgColor: '#4b5563',
                gridColor: '#6b7280',
                secondaryColor: '#10b981',
                tertiaryColor: '#f59e0b'
            },
            flowchart: {
                useMaxWidth: true,
                htmlLabels: true,
                curve: 'basis'
            },
            sequence: {
                diagramMarginX: 50,
                diagramMarginY: 10,
                actorMargin: 50,
                width: 150,
                height: 65,
                boxMargin: 10,
                boxTextMargin: 5,
                noteMargin: 10,
                messageMargin: 35,
                mirrorActors: true,
                bottomMarginAdj: 1,
                useMaxWidth: true,
                rightAngles: false,
                showSequenceNumbers: false
            },
            gantt: {
                useMaxWidth: true,
                leftPadding: 75,
                gridLineStartPadding: 35,
                fontSize: 11,
                sectionFontSize: 11,
                numberSectionStyles: 4
            }
        });
    }
}

// تحديد نوع مخطط Mermaid
function detectMermaidType(content) {
    const trimmedContent = content.trim().toLowerCase();

    if (trimmedContent.startsWith('graph td') || trimmedContent.startsWith('graph lr') ||
        trimmedContent.startsWith('graph bt') || trimmedContent.startsWith('graph rl')) {
        return 'graph';
    }
    if (trimmedContent.startsWith('flowchart')) {
        return 'flowchart';
    }
    if (trimmedContent.startsWith('sequencediagram') || trimmedContent.includes('participant')) {
        return 'sequence';
    }
    if (trimmedContent.startsWith('gantt')) {
        return 'gantt';
    }
    if (trimmedContent.startsWith('pie')) {
        return 'pie';
    }
    if (trimmedContent.startsWith('gitgraph')) {
        return 'gitgraph';
    }
    if (trimmedContent.startsWith('erdiagram') || trimmedContent.includes('||--||')) {
        return 'er';
    }
    if (trimmedContent.startsWith('journey')) {
        return 'journey';
    }

    return 'graph'; // افتراضي
}

// الحصول على تسمية نوع المخطط
function getMermaidTypeLabel(type) {
    const labels = {
        'graph': 'تدفق',
        'flowchart': 'مخطط انسيابي',
        'sequence': 'تسلسل',
        'gantt': 'جانت',
        'pie': 'دائري',
        'gitgraph': 'Git',
        'er': 'علاقات',
        'journey': 'رحلة المستخدم'
    };
    return labels[type] || 'تفاعلي';
}

// رسم مخطط Mermaid
async function renderMermaidDiagram(mermaidId, content) {
    try {
        if (typeof mermaid === 'undefined') {
            throw new Error('مكتبة Mermaid غير محملة');
        }

        const diagramElement = document.getElementById(`${mermaidId}-diagram`);
        if (!diagramElement) {
            console.error('عنصر المخطط غير موجود:', mermaidId);
            return;
        }

        // تنظيف المحتوى السابق
        diagramElement.innerHTML = '<div class="mermaid-loading">جاري تحميل المخطط...</div>';

        // تهيئة Mermaid إذا لم تكن مهيأة
        initializeMermaid();

        // إنشاء معرف فريد للمخطط
        const uniqueId = `mermaid-svg-${mermaidId}`;

        // رسم المخطط
        const { svg } = await mermaid.render(uniqueId, content);

        // عرض المخطط
        diagramElement.innerHTML = svg;

        // إضافة تفاعلية للمخطط
        const svgElement = diagramElement.querySelector('svg');
        if (svgElement) {
            svgElement.style.maxWidth = '100%';
            svgElement.style.height = 'auto';
            svgElement.style.display = 'block';
            svgElement.style.margin = '0 auto';
        }

    } catch (error) {
        console.error('خطأ في رسم مخطط Mermaid:', error);
        const diagramElement = document.getElementById(`${mermaidId}-diagram`);
        if (diagramElement) {
            diagramElement.innerHTML = `
                <div class="mermaid-error">
                    <i class="fas fa-exclamation-triangle"></i>
                    خطأ في رسم المخطط: ${error.message}
                </div>
            `;
        }

        // إزالة أي عناصر SVG خطأ من Mermaid
        removeMermaidErrorSVGs();
    }
}

// التنقل بين عرض المخطط والكود
function toggleMermaidView(mermaidId, viewType) {
    const diagramElement = document.getElementById(`${mermaidId}-diagram`);
    const codeElement = document.getElementById(`${mermaidId}-code`);
    const container = diagramElement.closest('.mermaid-container');

    if (!diagramElement || !codeElement || !container) {
        console.error('عناصر المخطط غير موجودة:', mermaidId);
        return;
    }

    // تحديث الأزرار
    const viewBtn = container.querySelector('.view-btn');
    const codeBtn = container.querySelector('.code-btn');

    if (viewType === 'diagram') {
        // عرض المخطط
        diagramElement.style.display = 'flex';
        codeElement.classList.remove('active');
        viewBtn.classList.add('active');
        codeBtn.classList.remove('active');
    } else if (viewType === 'code') {
        // عرض الكود
        diagramElement.style.display = 'none';
        codeElement.classList.add('active');
        viewBtn.classList.remove('active');
        codeBtn.classList.add('active');

        // تطبيق تمييز الكود
        const codeBlock = codeElement.querySelector('code');
        if (codeBlock && typeof Prism !== 'undefined') {
            Prism.highlightElement(codeBlock);
        }
    }
}

// نسخ كود Mermaid
function copyMermaidCode(mermaidId) {
    const codeElement = document.getElementById(`${mermaidId}-code`);
    if (!codeElement) {
        console.error('عنصر الكود غير موجود:', mermaidId);
        return;
    }

    const codeContent = codeElement.querySelector('code').textContent;

    navigator.clipboard.writeText(codeContent).then(() => {
        // إظهار رسالة نجاح
        const container = codeElement.closest('.mermaid-container');
        const copyBtn = container.querySelector('.copy-btn');

        const originalText = copyBtn.innerHTML;
        copyBtn.innerHTML = '<i class="fas fa-check"></i> تم النسخ';
        copyBtn.classList.add('active');

        setTimeout(() => {
            copyBtn.innerHTML = originalText;
            copyBtn.classList.remove('active');
        }, 2000);
    }).catch(err => {
        console.error('فشل في نسخ الكود:', err);

        // طريقة بديلة للنسخ
        const textArea = document.createElement('textarea');
        textArea.value = codeContent;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);

        // إظهار إشعار نجاح
        showSuccess('تم نسخ الكود إلى الحافظة!', 'نسخ ناجح');
        const container = codeElement.closest('.mermaid-container');
        const copyBtn = container.querySelector('.copy-btn');

        const originalText = copyBtn.innerHTML;
        copyBtn.innerHTML = '<i class="fas fa-check"></i> تم النسخ';
        copyBtn.classList.add('active');

        setTimeout(() => {
            copyBtn.innerHTML = originalText;
            copyBtn.classList.remove('active');
        }, 2000);
    });
}

// معالجة مخططات Mermaid المعلقة
function processPendingMermaidDiagrams() {
    if (!window.pendingMermaidDiagrams || window.pendingMermaidDiagrams.length === 0) {
        return;
    }

    // معالجة كل مخطط معلق
    window.pendingMermaidDiagrams.forEach(diagram => {
        setTimeout(() => {
            renderMermaidDiagram(diagram.id, diagram.content);
        }, 100);
    });

    // مسح القائمة بعد المعالجة
    window.pendingMermaidDiagrams = [];
}

// تبديل قائمة التحميل
function toggleDownloadMenu(mermaidId) {
    const dropdown = document.getElementById(`${mermaidId}-download-menu`);
    if (!dropdown) return;

    // إغلاق جميع القوائم الأخرى
    document.querySelectorAll('.mermaid-download-dropdown.show').forEach(menu => {
        if (menu.id !== `${mermaidId}-download-menu`) {
            menu.classList.remove('show');
        }
    });

    // تبديل القائمة الحالية
    dropdown.classList.toggle('show');

    // إغلاق القائمة عند النقر خارجها
    if (dropdown.classList.contains('show')) {
        setTimeout(() => {
            document.addEventListener('click', function closeMenu(e) {
                if (!dropdown.contains(e.target) && !e.target.closest('.download-btn')) {
                    dropdown.classList.remove('show');
                    document.removeEventListener('click', closeMenu);
                }
            });
        }, 100);
    }
}

// تحميل مخطط Mermaid
async function downloadMermaidDiagram(mermaidId, format) {
    try {
        const diagramElement = document.getElementById(`${mermaidId}-diagram`);
        if (!diagramElement) {
            console.error('عنصر المخطط غير موجود:', mermaidId);
            return;
        }

        const svgElement = diagramElement.querySelector('svg');
        if (!svgElement) {
            console.error('عنصر SVG غير موجود في المخطط:', mermaidId);
            return;
        }

        // إغلاق قائمة التحميل
        const dropdown = document.getElementById(`${mermaidId}-download-menu`);
        if (dropdown) {
            dropdown.classList.remove('show');
        }

        // تحديد اسم الملف
        const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
        const fileName = `mermaid-diagram-${timestamp}`;

        if (format === 'svg') {
            await downloadAsSVG(svgElement, fileName);
        } else if (format === 'png') {
            await downloadAsPNG(svgElement, fileName);
        } else if (format === 'pdf') {
            await downloadAsPDF(svgElement, fileName);
        }

    } catch (error) {
        console.error('خطأ في تحميل المخطط:', error);
        showError('حدث خطأ أثناء تحميل المخطط. يرجى المحاولة مرة أخرى.', 'خطأ في التحميل');
    }
}

// تحميل كـ SVG
async function downloadAsSVG(svgElement, fileName) {
    try {
        // نسخ عنصر SVG
        const svgClone = svgElement.cloneNode(true);

        // إضافة namespace و XML declaration
        svgClone.setAttribute('xmlns', 'http://www.w3.org/2000/svg');
        svgClone.setAttribute('xmlns:xlink', 'http://www.w3.org/1999/xlink');

        // تحسين الأنماط
        const styles = `
            <style>
                .node rect, .node circle, .node ellipse, .node polygon {
                    fill: #f9f9f9;
                    stroke: #333;
                    stroke-width: 1px;
                }
                .edgePath path {
                    stroke: #333;
                    stroke-width: 1.5px;
                    fill: none;
                }
                .edgeLabel {
                    background-color: white;
                    font-family: Arial, sans-serif;
                    font-size: 12px;
                }
                text {
                    font-family: Arial, sans-serif;
                    font-size: 14px;
                    fill: #333;
                }
            </style>
        `;

        svgClone.insertAdjacentHTML('afterbegin', styles);

        // تحويل إلى string
        const svgString = new XMLSerializer().serializeToString(svgClone);
        const blob = new Blob([svgString], { type: 'image/svg+xml' });

        // تحميل الملف
        const url = URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `${fileName}.svg`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(url);

        console.log('تم تحميل ملف SVG بنجاح');

    } catch (error) {
        console.error('خطأ في تحميل SVG:', error);
        throw error;
    }
}

// تحميل كـ PNG
async function downloadAsPNG(svgElement, fileName) {
    try {
        console.log('🖼️ بدء تحميل PNG...');

        // إنشاء canvas
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');

        // الحصول على أبعاد SVG بطريقة محسنة
        let svgWidth, svgHeight;

        // محاولة الحصول على الأبعاد من viewBox أولاً
        if (svgElement.viewBox && svgElement.viewBox.baseVal) {
            svgWidth = svgElement.viewBox.baseVal.width;
            svgHeight = svgElement.viewBox.baseVal.height;
        } else {
            // الحصول على الأبعاد من width/height attributes
            svgWidth = parseFloat(svgElement.getAttribute('width')) || 800;
            svgHeight = parseFloat(svgElement.getAttribute('height')) || 600;
        }

        // التأكد من وجود أبعاد صالحة
        if (svgWidth <= 0 || svgHeight <= 0) {
            svgWidth = 800;
            svgHeight = 600;
        }

        console.log(`📏 أبعاد المخطط: ${svgWidth} x ${svgHeight}`);

        // تعيين أبعاد canvas مع جودة عالية
        const scale = 3; // جودة أعلى للحصول على صورة أوضح
        canvas.width = svgWidth * scale;
        canvas.height = svgHeight * scale;

        // تطبيق التحجيم
        ctx.scale(scale, scale);

        // خلفية بيضاء نظيفة
        ctx.fillStyle = '#ffffff';
        ctx.fillRect(0, 0, svgWidth, svgHeight);

        // نسخ SVG وتحسينه
        const svgClone = svgElement.cloneNode(true);
        svgClone.setAttribute('width', svgWidth.toString());
        svgClone.setAttribute('height', svgHeight.toString());

        // إضافة أنماط محسنة للـ SVG
        const styleElement = document.createElementNS('http://www.w3.org/2000/svg', 'style');
        styleElement.textContent = `
            .node rect, .node circle, .node ellipse, .node polygon, .node path {
                fill: #f8f9fa !important;
                stroke: #212529 !important;
                stroke-width: 2px !important;
            }
            .edgePath path {
                stroke: #495057 !important;
                stroke-width: 2px !important;
                fill: none !important;
            }
            .edgeLabel {
                background-color: white !important;
                font-family: 'Arial', sans-serif !important;
                font-size: 14px !important;
                font-weight: 500 !important;
            }
            text {
                font-family: 'Arial', sans-serif !important;
                font-size: 14px !important;
                fill: #212529 !important;
                font-weight: 500 !important;
            }
            .label {
                color: #212529 !important;
                font-weight: 500 !important;
            }
        `;
        svgClone.insertBefore(styleElement, svgClone.firstChild);

        // تحويل SVG إلى data URL مباشرة (تجنب CORS)
        const svgString = new XMLSerializer().serializeToString(svgClone);
        const svgDataUrl = 'data:image/svg+xml;charset=utf-8,' + encodeURIComponent(svgString);

        // رسم SVG على canvas
        const img = new Image();

        img.onload = function() {
            console.log('✅ تم تحميل SVG بنجاح، جاري الرسم...');

            try {
                // رسم الصورة على canvas
                ctx.drawImage(img, 0, 0, svgWidth, svgHeight);

                // تحويل canvas إلى PNG وتحميله
                canvas.toBlob(function(blob) {
                    if (blob) {
                        const url = URL.createObjectURL(blob);
                        const link = document.createElement('a');
                        link.href = url;
                        link.download = `${fileName}.png`;
                        link.style.display = 'none';

                        document.body.appendChild(link);
                        link.click();
                        document.body.removeChild(link);

                        // تنظيف الذاكرة
                        setTimeout(() => {
                            URL.revokeObjectURL(url);
                        }, 1000);

                        console.log('🎉 تم تحميل ملف PNG بنجاح!');

                        // إظهار رسالة نجاح للمستخدم
                        showDownloadSuccess('PNG');
                    } else {
                        throw new Error('فشل في إنشاء blob للـ PNG');
                    }
                }, 'image/png', 1.0);

            } catch (canvasError) {
                console.error('❌ خطأ في Canvas:', canvasError);
                // طريقة بديلة: استخدام canvas.toDataURL
                try {
                    const dataUrl = canvas.toDataURL('image/png', 1.0);
                    const link = document.createElement('a');
                    link.href = dataUrl;
                    link.download = `${fileName}.png`;
                    link.style.display = 'none';

                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);

                    console.log('🎉 تم تحميل ملف PNG بنجاح (طريقة بديلة)!');
                    showDownloadSuccess('PNG');

                } catch (dataUrlError) {
                    console.error('❌ فشل في الطريقة البديلة:', dataUrlError);
                    throw new Error('فشل في تحويل Canvas إلى PNG');
                }
            }
        };

        img.onerror = function(error) {
            console.error('❌ فشل في تحميل SVG للتحويل إلى PNG:', error);
            throw new Error('فشل في تحويل SVG إلى PNG');
        };

        img.src = svgDataUrl;

    } catch (error) {
        console.error('❌ خطأ في تحميل PNG:', error);
        showDownloadError('PNG', error.message);
        throw error;
    }
}

// تحميل كـ PDF (يتطلب مكتبة jsPDF)
async function downloadAsPDF(svgElement, fileName) {
    try {
        console.log('📄 بدء تحميل PDF...');

        // إظهار رسالة تحميل
        showDownloadProgress('PDF');

        // التحقق من وجود مكتبة jsPDF
        if (typeof window.jsPDF === 'undefined' &&
            !(window.jspdf && typeof window.jspdf.jsPDF !== 'undefined')) {
            console.log('📦 تحميل مكتبة jsPDF...');
            await loadJsPDF();
        } else {
            console.log('✅ مكتبة jsPDF متاحة بالفعل');
        }

        // التحقق من طريقة استدعاء jsPDF الصحيحة
        let jsPDF;

        if (typeof window.jsPDF !== 'undefined') {
            jsPDF = window.jsPDF;
            console.log('✅ استخدام window.jsPDF');
        } else if (window.jspdf && typeof window.jspdf.jsPDF !== 'undefined') {
            jsPDF = window.jspdf.jsPDF;
            console.log('✅ استخدام window.jspdf.jsPDF');
        } else {
            console.error('❌ مكتبة jsPDF غير متاحة بعد التحميل');
            throw new Error('مكتبة jsPDF غير متاحة');
        }

        // التأكد من أن jsPDF هو constructor صالح
        if (typeof jsPDF !== 'function') {
            console.error('❌ jsPDF ليس constructor صالح:', typeof jsPDF);
            throw new Error('jsPDF ليس constructor صالح');
        }

        // الحصول على أبعاد SVG بطريقة محسنة
        let svgWidth, svgHeight;

        if (svgElement.viewBox && svgElement.viewBox.baseVal) {
            svgWidth = svgElement.viewBox.baseVal.width;
            svgHeight = svgElement.viewBox.baseVal.height;
        } else {
            svgWidth = parseFloat(svgElement.getAttribute('width')) || 800;
            svgHeight = parseFloat(svgElement.getAttribute('height')) || 600;
        }

        // التأكد من وجود أبعاد صالحة
        if (svgWidth <= 0 || svgHeight <= 0) {
            svgWidth = 800;
            svgHeight = 600;
        }

        console.log(`📏 أبعاد المخطط للـ PDF: ${svgWidth} x ${svgHeight}`);

        // تحديد اتجاه الصفحة بناءً على أبعاد المخطط
        const orientation = svgWidth > svgHeight ? 'landscape' : 'portrait';

        // إنشاء مستند PDF
        const pdf = new jsPDF({
            orientation: orientation,
            unit: 'pt',
            format: 'a4'
        });

        // الحصول على أبعاد الصفحة
        const pageWidth = pdf.internal.pageSize.getWidth();
        const pageHeight = pdf.internal.pageSize.getHeight();

        // تحويل SVG إلى PNG أولاً بجودة عالية
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');

        // جودة عالية للـ PDF
        const scale = 2;
        canvas.width = svgWidth * scale;
        canvas.height = svgHeight * scale;

        ctx.scale(scale, scale);

        // خلفية بيضاء نظيفة
        ctx.fillStyle = '#ffffff';
        ctx.fillRect(0, 0, svgWidth, svgHeight);

        // نسخ وتحسين SVG
        const svgClone = svgElement.cloneNode(true);
        svgClone.setAttribute('width', svgWidth.toString());
        svgClone.setAttribute('height', svgHeight.toString());

        // إضافة أنماط محسنة للطباعة
        const styleElement = document.createElementNS('http://www.w3.org/2000/svg', 'style');
        styleElement.textContent = `
            .node rect, .node circle, .node ellipse, .node polygon, .node path {
                fill: #f8f9fa !important;
                stroke: #000000 !important;
                stroke-width: 1.5px !important;
            }
            .edgePath path {
                stroke: #333333 !important;
                stroke-width: 1.5px !important;
                fill: none !important;
            }
            .edgeLabel {
                background-color: white !important;
                font-family: 'Arial', sans-serif !important;
                font-size: 12px !important;
                font-weight: 600 !important;
            }
            text {
                font-family: 'Arial', sans-serif !important;
                font-size: 12px !important;
                fill: #000000 !important;
                font-weight: 600 !important;
            }
        `;
        svgClone.insertBefore(styleElement, svgClone.firstChild);

        const svgString = new XMLSerializer().serializeToString(svgClone);
        const svgDataUrl = 'data:image/svg+xml;charset=utf-8,' + encodeURIComponent(svgString);

        const img = new Image();

        img.onload = function() {
            console.log('✅ تم تحميل SVG للـ PDF، جاري الرسم...');

            try {
                ctx.drawImage(img, 0, 0, svgWidth, svgHeight);

                // إضافة الصورة إلى PDF
                const imgData = canvas.toDataURL('image/png', 1.0);

                // حساب الأبعاد للـ PDF مع هوامش
                const margin = 40; // هامش 40pt
                const availableWidth = pageWidth - (margin * 2);
                const availableHeight = pageHeight - (margin * 2);

                const ratio = Math.min(availableWidth / svgWidth, availableHeight / svgHeight);

                const finalWidth = svgWidth * ratio;
                const finalHeight = svgHeight * ratio;
                const x = (pageWidth - finalWidth) / 2;
                const y = (pageHeight - finalHeight) / 2;

                // إضافة عنوان للمستند
                pdf.setFontSize(16);
                pdf.setFont('helvetica', 'bold');
                pdf.text('Mermaid Diagram', margin, margin - 10);

                // إضافة تاريخ
                pdf.setFontSize(10);
                pdf.setFont('helvetica', 'normal');
                const date = new Date().toLocaleDateString('ar-SA');
                pdf.text(`تاريخ الإنشاء: ${date}`, pageWidth - margin - 100, margin - 10);

                // إضافة المخطط
                pdf.addImage(imgData, 'PNG', x, y, finalWidth, finalHeight, undefined, 'FAST');

                // حفظ PDF
                pdf.save(`${fileName}.pdf`);

                console.log('🎉 تم تحميل ملف PDF بنجاح!');
                showDownloadSuccess('PDF');

            } catch (canvasError) {
                console.error('❌ خطأ في Canvas للـ PDF:', canvasError);
                showDownloadError('PDF', 'فشل في تحويل المخطط إلى PDF');
            }
        };

        img.onerror = function(error) {
            console.error('❌ فشل في تحويل SVG إلى PDF:', error);
            showDownloadError('PDF', 'فشل في تحويل المخطط إلى PDF');
        };

        img.src = svgDataUrl;

    } catch (error) {
        console.error('❌ خطأ في تحميل PDF:', error);

        // طريقة بديلة: تحميل كـ PNG بدلاً من PDF
        console.log('🔄 محاولة تحميل كـ PNG بدلاً من PDF...');
        try {
            await downloadAsPNG(svgElement, fileName + '_as_image');
            showDownloadSuccess('PNG (بديل عن PDF)');
        } catch (pngError) {
            console.error('❌ فشل في الطريقة البديلة أيضاً:', pngError);
            showDownloadError('PDF', 'فشل في تحميل PDF. يرجى المحاولة مرة أخرى أو استخدام PNG/SVG.');
        }
    }
}

// تحميل مكتبة jsPDF ديناميكياً
async function loadJsPDF() {
    return new Promise((resolve, reject) => {
        // التحقق من وجود المكتبة بطرق مختلفة
        if (typeof window.jsPDF !== 'undefined') {
            console.log('✅ مكتبة jsPDF موجودة بالفعل');
            resolve();
            return;
        }

        if (window.jspdf && typeof window.jspdf.jsPDF !== 'undefined') {
            console.log('✅ مكتبة jsPDF موجودة بالفعل (jspdf namespace)');
            resolve();
            return;
        }

        console.log('📦 بدء تحميل مكتبة jsPDF...');

        // جرب عدة مصادر مختلفة
        const sources = [
            'https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js',
            'https://unpkg.com/jspdf@2.5.1/dist/jspdf.umd.min.js',
            'https://cdn.jsdelivr.net/npm/jspdf@2.5.1/dist/jspdf.umd.min.js'
        ];

        let currentSourceIndex = 0;

        function tryLoadSource() {
            if (currentSourceIndex >= sources.length) {
                console.error('❌ فشل في تحميل مكتبة jsPDF من جميع المصادر');
                reject(new Error('فشل في تحميل مكتبة jsPDF من جميع المصادر'));
                return;
            }

            const script = document.createElement('script');
            script.src = sources[currentSourceIndex];

            script.onload = () => {
                console.log(`📦 تم تحميل من المصدر ${currentSourceIndex + 1}`);

                // انتظار قصير للتأكد من تحميل المكتبة
                setTimeout(() => {
                    if (typeof window.jsPDF !== 'undefined') {
                        console.log('✅ تم تحميل مكتبة jsPDF بنجاح (window.jsPDF)');
                        resolve();
                    } else if (window.jspdf && typeof window.jspdf.jsPDF !== 'undefined') {
                        console.log('✅ تم تحميل مكتبة jsPDF بنجاح (window.jspdf.jsPDF)');
                        // إنشاء مرجع مباشر للسهولة
                        window.jsPDF = window.jspdf.jsPDF;
                        resolve();
                    } else {
                        console.warn(`⚠️ المصدر ${currentSourceIndex + 1} لم يعمل، جاري المحاولة التالية...`);
                        currentSourceIndex++;
                        tryLoadSource();
                    }
                }, 200);
            };

            script.onerror = () => {
                console.warn(`⚠️ فشل تحميل من المصدر ${currentSourceIndex + 1}`);
                currentSourceIndex++;
                tryLoadSource();
            };

            document.head.appendChild(script);
        }

        tryLoadSource();
    });
}

// إظهار رسالة نجاح التحميل
function showDownloadSuccess(format) {
    // إزالة رسالة التقدم إن وجدت
    const progressMessage = document.getElementById(`download-progress-${format}`);
    if (progressMessage && progressMessage.parentNode) {
        progressMessage.parentNode.removeChild(progressMessage);
    }

    // إنشاء رسالة نجاح مؤقتة
    const message = document.createElement('div');
    message.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: #10b981;
        color: white;
        padding: 12px 20px;
        border-radius: 8px;
        font-size: 14px;
        font-weight: 500;
        z-index: 10000;
        box-shadow: 0 4px 12px rgba(0,0,0,0.3);
        animation: slideIn 0.3s ease-out;
    `;

    message.innerHTML = `
        <i class="fas fa-check-circle" style="margin-left: 8px;"></i>
        تم تحميل ملف ${format} بنجاح!
    `;

    // إضافة CSS للانيميشن
    if (!document.getElementById('download-animations')) {
        const style = document.createElement('style');
        style.id = 'download-animations';
        style.textContent = `
            @keyframes slideIn {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
            @keyframes slideOut {
                from { transform: translateX(0); opacity: 1; }
                to { transform: translateX(100%); opacity: 0; }
            }
        `;
        document.head.appendChild(style);
    }

    document.body.appendChild(message);

    // إزالة الرسالة بعد 3 ثوان
    setTimeout(() => {
        message.style.animation = 'slideOut 0.3s ease-in';
        setTimeout(() => {
            if (message.parentNode) {
                message.parentNode.removeChild(message);
            }
        }, 300);
    }, 3000);
}

// إظهار رسالة خطأ التحميل
function showDownloadError(format, errorMessage) {
    // إزالة رسالة التقدم إن وجدت
    const progressMessage = document.getElementById(`download-progress-${format}`);
    if (progressMessage && progressMessage.parentNode) {
        progressMessage.parentNode.removeChild(progressMessage);
    }

    const message = document.createElement('div');
    message.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: #ef4444;
        color: white;
        padding: 12px 20px;
        border-radius: 8px;
        font-size: 14px;
        font-weight: 500;
        z-index: 10000;
        box-shadow: 0 4px 12px rgba(0,0,0,0.3);
        animation: slideIn 0.3s ease-out;
        max-width: 300px;
    `;

    message.innerHTML = `
        <i class="fas fa-exclamation-triangle" style="margin-left: 8px;"></i>
        فشل تحميل ${format}: ${errorMessage}
    `;

    document.body.appendChild(message);

    // إزالة الرسالة بعد 5 ثوان
    setTimeout(() => {
        message.style.animation = 'slideOut 0.3s ease-in';
        setTimeout(() => {
            if (message.parentNode) {
                message.parentNode.removeChild(message);
            }
        }, 300);
    }, 5000);
}

// إظهار رسالة تقدم التحميل
function showDownloadProgress(format) {
    const message = document.createElement('div');
    message.id = `download-progress-${format}`;
    message.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: #3b82f6;
        color: white;
        padding: 12px 20px;
        border-radius: 8px;
        font-size: 14px;
        font-weight: 500;
        z-index: 10000;
        box-shadow: 0 4px 12px rgba(0,0,0,0.3);
        animation: slideIn 0.3s ease-out;
    `;

    message.innerHTML = `
        <i class="fas fa-spinner fa-spin" style="margin-left: 8px;"></i>
        جاري تحضير ملف ${format}...
    `;

    document.body.appendChild(message);

    // إزالة الرسالة بعد 10 ثوان (في حالة عدم استدعاء دالة النجاح/الخطأ)
    setTimeout(() => {
        const progressMessage = document.getElementById(`download-progress-${format}`);
        if (progressMessage && progressMessage.parentNode) {
            progressMessage.style.animation = 'slideOut 0.3s ease-in';
            setTimeout(() => {
                if (progressMessage.parentNode) {
                    progressMessage.parentNode.removeChild(progressMessage);
                }
            }, 300);
        }
    }, 10000);
}

// دوال التكبير والتصغير
function zoomMermaidDiagram(mermaidId, action) {
    const diagramElement = document.getElementById(`${mermaidId}-diagram`);
    if (!diagramElement) return;

    // الحصول على التكبير الحالي
    let currentZoom = parseFloat(diagramElement.dataset.zoom || '1');

    switch (action) {
        case 'in':
            currentZoom = Math.min(currentZoom * 1.2, 3); // حد أقصى 3x
            break;
        case 'out':
            currentZoom = Math.max(currentZoom / 1.2, 0.3); // حد أدنى 0.3x
            break;
        case 'reset':
            currentZoom = 1;
            break;
    }

    // تطبيق التكبير
    diagramElement.style.transform = `scale(${currentZoom})`;
    diagramElement.dataset.zoom = currentZoom.toString();

    // إضافة/إزالة class للتحكم في المؤشر
    if (currentZoom !== 1) {
        diagramElement.classList.add('zoomed');
        enableDragForZoomedDiagram(diagramElement);
    } else {
        diagramElement.classList.remove('zoomed');
        disableDragForDiagram(diagramElement);
    }

    console.log(`🔍 تم تغيير التكبير إلى: ${currentZoom.toFixed(2)}x`);
}

// تفعيل السحب للمخططات المكبرة
function enableDragForZoomedDiagram(element) {
    let isDragging = false;
    let startX, startY, scrollLeft, scrollTop;

    element.addEventListener('mousedown', startDrag);
    element.addEventListener('mousemove', drag);
    element.addEventListener('mouseup', stopDrag);
    element.addEventListener('mouseleave', stopDrag);

    function startDrag(e) {
        isDragging = true;
        startX = e.pageX - element.offsetLeft;
        startY = e.pageY - element.offsetTop;
        scrollLeft = element.scrollLeft;
        scrollTop = element.scrollTop;
        element.style.cursor = 'grabbing';
    }

    function drag(e) {
        if (!isDragging) return;
        e.preventDefault();
        const x = e.pageX - element.offsetLeft;
        const y = e.pageY - element.offsetTop;
        const walkX = (x - startX) * 2;
        const walkY = (y - startY) * 2;
        element.scrollLeft = scrollLeft - walkX;
        element.scrollTop = scrollTop - walkY;
    }

    function stopDrag() {
        isDragging = false;
        element.style.cursor = 'grab';
    }
}

// إلغاء السحب للمخططات
function disableDragForDiagram(element) {
    element.style.cursor = 'default';
    element.style.transform = '';
    element.scrollLeft = 0;
    element.scrollTop = 0;
}

// تبديل قائمة التصميم
function toggleThemeMenu(mermaidId) {
    const dropdown = document.getElementById(`${mermaidId}-theme-menu`);
    if (!dropdown) return;

    // إغلاق جميع القوائم الأخرى
    document.querySelectorAll('.mermaid-theme-dropdown.show').forEach(menu => {
        if (menu.id !== `${mermaidId}-theme-menu`) {
            menu.classList.remove('show');
        }
    });

    // تبديل القائمة الحالية
    dropdown.classList.toggle('show');

    // إغلاق القائمة عند النقر خارجها
    if (dropdown.classList.contains('show')) {
        setTimeout(() => {
            document.addEventListener('click', function closeMenu(e) {
                if (!dropdown.contains(e.target) && !e.target.closest('.theme-btn')) {
                    dropdown.classList.remove('show');
                    document.removeEventListener('click', closeMenu);
                }
            });
        }, 100);
    }
}

// تغيير تصميم المخطط
function changeMermaidTheme(mermaidId, theme) {
    const container = document.querySelector(`[data-mermaid-id="${mermaidId}"]`);
    const diagramElement = document.getElementById(`${mermaidId}-diagram`);

    if (!container || !diagramElement) return;

    // إغلاق قائمة التصميم
    const dropdown = document.getElementById(`${mermaidId}-theme-menu`);
    if (dropdown) {
        dropdown.classList.remove('show');
    }

    // تعيين التصميم الجديد
    container.dataset.theme = theme;

    // الحصول على محتوى المخطط الأصلي
    const codeElement = document.getElementById(`${mermaidId}-code`);
    if (!codeElement) return;

    const mermaidCode = codeElement.querySelector('code').textContent;

    // إعادة رسم المخطط بالتصميم الجديد
    renderMermaidDiagramWithTheme(mermaidId, mermaidCode, theme);

    console.log(`🎨 تم تغيير التصميم إلى: ${theme}`);
}

// رسم المخطط مع تصميم محدد
async function renderMermaidDiagramWithTheme(mermaidId, content, theme) {
    const diagramElement = document.getElementById(`${mermaidId}-diagram`);
    if (!diagramElement) return;

    // إظهار رسالة تحميل
    diagramElement.innerHTML = '<div class="mermaid-loading">جاري تطبيق التصميم...</div>';

    try {
        // تكوين التصميم
        const themeConfig = getMermaidThemeConfig(theme);

        // تهيئة Mermaid مع التصميم الجديد
        if (window.mermaid) {
            window.mermaid.initialize({
                startOnLoad: false,
                theme: themeConfig.theme,
                themeVariables: themeConfig.variables,
                flowchart: {
                    useMaxWidth: true,
                    htmlLabels: true
                },
                sequence: {
                    useMaxWidth: true
                },
                gantt: {
                    useMaxWidth: true
                }
            });

            // رسم المخطط
            const { svg } = await window.mermaid.render(`${mermaidId}-svg`, content);
            diagramElement.innerHTML = svg;

            console.log(`✅ تم تطبيق التصميم ${theme} بنجاح`);
        }
    } catch (error) {
        console.error('❌ خطأ في تطبيق التصميم:', error);
        diagramElement.innerHTML = '<div class="mermaid-error">خطأ في تطبيق التصميم</div>';
    }
}

// الحصول على تكوين التصميم
function getMermaidThemeConfig(theme) {
    const configs = {
        default: {
            theme: 'default',
            variables: {
                primaryColor: '#0066cc',
                primaryTextColor: '#ffffff',
                primaryBorderColor: '#004499',
                lineColor: '#333333',
                sectionBkgColor: '#f0f8ff',
                altSectionBkgColor: '#e6f3ff'
            }
        },
        dark: {
            theme: 'dark',
            variables: {
                primaryColor: '#4a90e2',
                primaryTextColor: '#ffffff',
                primaryBorderColor: '#357abd',
                lineColor: '#cccccc',
                sectionBkgColor: '#2d2d2d',
                altSectionBkgColor: '#404040'
            }
        },
        forest: {
            theme: 'forest',
            variables: {
                primaryColor: '#228B22',
                primaryTextColor: '#ffffff',
                primaryBorderColor: '#006400',
                lineColor: '#2d5a2d',
                sectionBkgColor: '#f0f8f0',
                altSectionBkgColor: '#e6f3e6'
            }
        },
        neutral: {
            theme: 'neutral',
            variables: {
                primaryColor: '#666666',
                primaryTextColor: '#ffffff',
                primaryBorderColor: '#444444',
                lineColor: '#555555',
                sectionBkgColor: '#f5f5f5',
                altSectionBkgColor: '#eeeeee'
            }
        },
        base: {
            theme: 'base',
            variables: {
                primaryColor: '#ff6b6b',
                primaryTextColor: '#ffffff',
                primaryBorderColor: '#e55555',
                lineColor: '#cc5555',
                sectionBkgColor: '#fff5f5',
                altSectionBkgColor: '#ffe6e6'
            }
        }
    };

    return configs[theme] || configs.default;
}

// دوال التكبير والتصغير
function zoomMermaidDiagram(mermaidId, action, centerX = null, centerY = null) {
    const diagramElement = document.getElementById(`${mermaidId}-diagram`);
    if (!diagramElement) return;

    const svgElement = diagramElement.querySelector('svg');
    if (!svgElement) return;

    // الحصول على التكبير الحالي أو تعيين القيمة الافتراضية
    let currentZoom = parseFloat(svgElement.dataset.zoom) || 1;
    let newZoom = currentZoom;

    // تحديد التكبير الجديد بزيادة ثابتة 25%
    switch (action) {
        case 'in':
            newZoom = Math.min(currentZoom + 0.25, 10); // زيادة ثابتة 25%
            break;
        case 'out':
            newZoom = Math.max(currentZoom - 0.25, 0.05); // تقليل ثابت 25%
            break;
        case 'reset':
            newZoom = 1;
            break;
    }

    // تطبيق التكبير مع الحفاظ على النقطة المركزية
    applyZoomTransform(svgElement, newZoom, centerX, centerY);

    // إضافة/إزالة class للتكبير
    if (newZoom !== 1) {
        diagramElement.classList.add('zoomed');
        enableDragging(diagramElement, svgElement);
        enableTouchZoom(diagramElement, svgElement, mermaidId);
        enableWheelZoom(diagramElement, svgElement, mermaidId);
    } else {
        diagramElement.classList.remove('zoomed');
        disableDragging(diagramElement);
        disableTouchZoom(diagramElement);
        disableWheelZoom(diagramElement);
    }

    // إظهار مؤشر التكبير
    showZoomIndicator(diagramElement, newZoom);
}

// تطبيق تحويل التكبير مع الحفاظ على الموضع
function applyZoomTransform(svgElement, zoom, centerX = null, centerY = null) {
    // الحصول على الإزاحة الحالية
    const currentTransform = svgElement.style.transform || '';
    const translateMatch = currentTransform.match(/translate\(([^,]+),\s*([^)]+)\)/);

    let translateX = parseFloat(svgElement.dataset.translateX) || 0;
    let translateY = parseFloat(svgElement.dataset.translateY) || 0;

    if (translateMatch) {
        translateX = parseFloat(translateMatch[1]) || 0;
        translateY = parseFloat(translateMatch[2]) || 0;
    }

    // إذا تم تحديد نقطة مركز (للتكبير باللمس/الماوس)
    if (centerX !== null && centerY !== null) {
        const rect = svgElement.getBoundingClientRect();
        const currentZoom = parseFloat(svgElement.dataset.zoom) || 1;

        // حساب الإزاحة الجديدة للحفاظ على النقطة المركزية
        const deltaZoom = zoom - currentZoom;
        const centerOffsetX = centerX - rect.left - rect.width / 2;
        const centerOffsetY = centerY - rect.top - rect.height / 2;

        translateX -= centerOffsetX * deltaZoom / currentZoom;
        translateY -= centerOffsetY * deltaZoom / currentZoom;
    }

    // تطبيق حدود للإزاحة لمنع فقدان المخطط
    const maxTranslate = 500; // حد أقصى للإزاحة
    translateX = Math.max(-maxTranslate, Math.min(translateX, maxTranslate));
    translateY = Math.max(-maxTranslate, Math.min(translateY, maxTranslate));

    // إذا كان التكبير 1 (الحجم الطبيعي)، أعد تعيين الإزاحة
    if (zoom === 1) {
        translateX = 0;
        translateY = 0;
    }

    // تطبيق التحويل
    svgElement.style.transform = `translate(${translateX}px, ${translateY}px) scale(${zoom})`;
    svgElement.dataset.zoom = zoom.toString();
    svgElement.dataset.translateX = translateX.toString();
    svgElement.dataset.translateY = translateY.toString();

    // تحسين دقة SVG عند التكبير
    enhanceSVGQuality(svgElement, zoom);
}

// إظهار مؤشر التكبير
function showZoomIndicator(diagramElement, zoom) {
    let indicator = diagramElement.querySelector('.zoom-indicator');

    if (!indicator) {
        indicator = document.createElement('div');
        indicator.className = 'zoom-indicator';
        diagramElement.appendChild(indicator);
    }

    indicator.textContent = `${Math.round(zoom * 100)}%`;
    indicator.classList.add('show');

    // إخفاء المؤشر بعد ثانيتين
    setTimeout(() => {
        indicator.classList.remove('show');
    }, 2000);
}

// تفعيل السحب للمخططات المكبرة
function enableDragging(diagramElement, svgElement) {
    let isDragging = false;
    let startX, startY, initialX = 0, initialY = 0;

    // الحصول على الموضع الحالي أو تعيين القيمة الافتراضية
    const currentTransform = svgElement.style.transform;
    const translateMatch = currentTransform.match(/translate\(([^,]+),\s*([^)]+)\)/);
    if (translateMatch) {
        initialX = parseFloat(translateMatch[1]) || 0;
        initialY = parseFloat(translateMatch[2]) || 0;
    }

    function startDrag(e) {
        isDragging = true;
        diagramElement.classList.add('dragging');

        const clientX = e.clientX || (e.touches && e.touches[0].clientX);
        const clientY = e.clientY || (e.touches && e.touches[0].clientY);

        startX = clientX - initialX;
        startY = clientY - initialY;

        e.preventDefault();
    }

    function drag(e) {
        if (!isDragging) return;

        const clientX = e.clientX || (e.touches && e.touches[0].clientX);
        const clientY = e.clientY || (e.touches && e.touches[0].clientY);

        initialX = clientX - startX;
        initialY = clientY - startY;

        const zoom = parseFloat(svgElement.dataset.zoom) || 1;
        svgElement.style.transform = `scale(${zoom}) translate(${initialX}px, ${initialY}px)`;

        e.preventDefault();
    }

    function endDrag() {
        isDragging = false;
        diagramElement.classList.remove('dragging');
    }

    // إزالة المستمعين السابقين
    diagramElement.removeEventListener('mousedown', startDrag);
    diagramElement.removeEventListener('touchstart', startDrag);

    // إضافة مستمعين جدد
    diagramElement.addEventListener('mousedown', startDrag);
    diagramElement.addEventListener('touchstart', startDrag);

    document.addEventListener('mousemove', drag);
    document.addEventListener('touchmove', drag);
    document.addEventListener('mouseup', endDrag);
    document.addEventListener('touchend', endDrag);

    // حفظ المراجع للإزالة لاحقاً
    diagramElement._dragHandlers = { startDrag, drag, endDrag };
}

// إلغاء تفعيل السحب
function disableDragging(diagramElement) {
    if (diagramElement._dragHandlers) {
        const { startDrag, drag, endDrag } = diagramElement._dragHandlers;

        diagramElement.removeEventListener('mousedown', startDrag);
        diagramElement.removeEventListener('touchstart', startDrag);
        document.removeEventListener('mousemove', drag);
        document.removeEventListener('touchmove', drag);
        document.removeEventListener('mouseup', endDrag);
        document.removeEventListener('touchend', endDrag);

        delete diagramElement._dragHandlers;
    }

    diagramElement.classList.remove('dragging');
}

// تفعيل التكبير باللمس (Pinch to Zoom) مع تحكم محسن
function enableTouchZoom(diagramElement, svgElement, mermaidId) {
    let initialDistance = 0;
    let initialZoom = 1;
    let isZooming = false;
    let centerX = 0;
    let centerY = 0;
    let lastZoom = 1;
    let zoomStartTime = 0;

    function getTouchDistance(touches) {
        const dx = touches[0].clientX - touches[1].clientX;
        const dy = touches[0].clientY - touches[1].clientY;
        return Math.sqrt(dx * dx + dy * dy);
    }

    function getTouchCenter(touches) {
        return {
            x: (touches[0].clientX + touches[1].clientX) / 2,
            y: (touches[0].clientY + touches[1].clientY) / 2
        };
    }

    function handleTouchStart(e) {
        if (e.touches.length === 2) {
            e.preventDefault();
            isZooming = true;
            zoomStartTime = Date.now();

            initialDistance = getTouchDistance(e.touches);
            initialZoom = parseFloat(svgElement.dataset.zoom) || 1;
            lastZoom = initialZoom;

            const center = getTouchCenter(e.touches);
            centerX = center.x;
            centerY = center.y;

            diagramElement.classList.add('zooming');
        }
    }

    function handleTouchMove(e) {
        if (e.touches.length === 2 && isZooming) {
            e.preventDefault();

            const currentDistance = getTouchDistance(e.touches);
            const rawScale = currentDistance / initialDistance;

            // تطبيق تنعيم للحركة لمنع القفزات المفاجئة
            const smoothingFactor = 0.1;
            const scale = lastZoom + (rawScale - 1) * smoothingFactor;

            let newZoom = initialZoom * scale;

            // تطبيق حدود أكثر تحفظاً للتكبير باللمس
            newZoom = Math.max(0.2, Math.min(newZoom, 5)); // 20% إلى 500%

            // منع التغييرات الصغيرة جداً
            const currentZoom = parseFloat(svgElement.dataset.zoom) || 1;
            if (Math.abs(newZoom - currentZoom) < 0.05) {
                return;
            }

            // تحديث النقطة المركزية باستمرار
            const newCenter = getTouchCenter(e.touches);
            centerX = newCenter.x;
            centerY = newCenter.y;

            // تطبيق التكبير مع الحفاظ على النقطة المركزية
            applyZoomTransform(svgElement, newZoom, centerX, centerY);

            // إظهار مؤشر التكبير
            showZoomIndicator(diagramElement, newZoom);

            lastZoom = scale;
        }
    }

    function handleTouchEnd(e) {
        if (e.touches.length < 2) {
            isZooming = false;
            diagramElement.classList.remove('zooming');

            // إذا كان التكبير سريعاً جداً أو مفرطاً، أعد تعيينه
            const zoomDuration = Date.now() - zoomStartTime;
            const currentZoom = parseFloat(svgElement.dataset.zoom) || 1;

            if (zoomDuration < 200 && (currentZoom > 4 || currentZoom < 0.3)) {
                console.log('🔄 إعادة تعيين التكبير السريع');
                showZoomWarning(diagramElement, 'تم إعادة تعيين التكبير تلقائياً');
                setTimeout(() => {
                    zoomMermaidDiagram(mermaidId, 'reset');
                }, 100);
            } else if (currentZoom > 3 || currentZoom < 0.5) {
                showZoomWarning(diagramElement, 'استخدم زر إعادة التعيين إذا فقدت المخطط');
            }
        }
    }

    // إضافة مستمعي الأحداث
    diagramElement.addEventListener('touchstart', handleTouchStart, { passive: false });
    diagramElement.addEventListener('touchmove', handleTouchMove, { passive: false });
    diagramElement.addEventListener('touchend', handleTouchEnd);

    // حفظ المراجع للإزالة لاحقاً
    diagramElement._touchZoomHandlers = {
        handleTouchStart,
        handleTouchMove,
        handleTouchEnd
    };
}

// إلغاء تفعيل التكبير باللمس
function disableTouchZoom(diagramElement) {
    if (diagramElement._touchZoomHandlers) {
        const { handleTouchStart, handleTouchMove, handleTouchEnd } = diagramElement._touchZoomHandlers;

        diagramElement.removeEventListener('touchstart', handleTouchStart);
        diagramElement.removeEventListener('touchmove', handleTouchMove);
        diagramElement.removeEventListener('touchend', handleTouchEnd);

        delete diagramElement._touchZoomHandlers;
    }

    diagramElement.classList.remove('zooming');
}

// تفعيل التكبير بعجلة الماوس
function enableWheelZoom(diagramElement, svgElement, mermaidId) {
    function handleWheel(e) {
        // التحقق من أن المؤشر فوق المخطط
        if (!diagramElement.contains(e.target)) return;

        // منع التمرير الافتراضي
        e.preventDefault();

        // الحصول على التكبير الحالي
        const currentZoom = parseFloat(svgElement.dataset.zoom) || 1;

        // تحديد اتجاه التكبير بزيادة ثابتة
        const delta = e.deltaY > 0 ? -0.1 : 0.1; // زيادة/تقليل ثابت 10%
        let newZoom = currentZoom + delta;

        // تطبيق الحدود
        newZoom = Math.max(0.05, Math.min(newZoom, 10)); // 5% إلى 1000%

        // الحصول على موضع المؤشر كنقطة مركز
        const rect = diagramElement.getBoundingClientRect();
        const centerX = e.clientX;
        const centerY = e.clientY;

        // تطبيق التكبير مع الحفاظ على النقطة المركزية
        applyZoomTransform(svgElement, newZoom, centerX, centerY);

        // إظهار مؤشر التكبير
        showZoomIndicator(diagramElement, newZoom);
    }

    // إضافة مستمع الحدث
    diagramElement.addEventListener('wheel', handleWheel, { passive: false });

    // حفظ المرجع للإزالة لاحقاً
    diagramElement._wheelZoomHandler = handleWheel;
}

// إلغاء تفعيل التكبير بعجلة الماوس
function disableWheelZoom(diagramElement) {
    if (diagramElement._wheelZoomHandler) {
        diagramElement.removeEventListener('wheel', diagramElement._wheelZoomHandler);
        delete diagramElement._wheelZoomHandler;
    }
}

// إظهار تحذير التكبير
function showZoomWarning(diagramElement, message) {
    // إزالة التحذير السابق إن وجد
    const existingWarning = diagramElement.querySelector('.zoom-warning');
    if (existingWarning) {
        existingWarning.remove();
    }

    // إنشاء تحذير جديد
    const warning = document.createElement('div');
    warning.className = 'zoom-warning';
    warning.textContent = message;

    diagramElement.appendChild(warning);

    // إزالة التحذير بعد 3 ثوانٍ
    setTimeout(() => {
        if (warning.parentNode) {
            warning.remove();
        }
    }, 3000);
}

// تحسين دقة SVG عند التكبير
function enhanceSVGQuality(svgElement, zoom) {
    try {
        // الحصول على الأبعاد الأصلية
        const originalWidth = parseFloat(svgElement.getAttribute('width')) || svgElement.getBoundingClientRect().width;
        const originalHeight = parseFloat(svgElement.getAttribute('height')) || svgElement.getBoundingClientRect().height;

        // حساب الأبعاد الجديدة بناءً على التكبير
        const scaledWidth = originalWidth * zoom;
        const scaledHeight = originalHeight * zoom;

        // تحديث viewBox للحفاظ على الدقة
        const viewBox = svgElement.getAttribute('viewBox');
        if (viewBox) {
            const [x, y, w, h] = viewBox.split(' ').map(Number);
            // الحفاظ على viewBox الأصلي لضمان الدقة
            svgElement.setAttribute('viewBox', `${x} ${y} ${w} ${h}`);
        }

        // تحسين دقة النصوص عند التكبير العالي
        if (zoom > 2) {
            const textElements = svgElement.querySelectorAll('text');
            textElements.forEach(text => {
                text.style.textRendering = 'geometricPrecision';
                text.style.shapeRendering = 'crispEdges';
            });
        }

        // تحسين دقة الخطوط والأشكال
        const pathElements = svgElement.querySelectorAll('path, line, polyline, polygon, rect, circle, ellipse');
        pathElements.forEach(element => {
            element.style.shapeRendering = 'geometricPrecision';
            element.style.vectorEffect = 'non-scaling-stroke';
        });

        // تحسين دقة الأسهم والعلامات
        const markerElements = svgElement.querySelectorAll('marker');
        markerElements.forEach(marker => {
            marker.style.shapeRendering = 'geometricPrecision';
        });

        // تطبيق تحسينات إضافية للتكبير العالي
        const diagramElement = svgElement.closest('.mermaid-diagram');
        if (zoom > 3) {
            svgElement.style.imageRendering = 'crisp-edges';
            svgElement.style.shapeRendering = 'geometricPrecision';
            if (diagramElement) {
                diagramElement.classList.add('high-zoom');
            }
        } else {
            svgElement.style.imageRendering = 'auto';
            svgElement.style.shapeRendering = 'auto';
            if (diagramElement) {
                diagramElement.classList.remove('high-zoom');
            }
        }

        // تحسين إضافي للشاشات عالية الدقة
        if (window.devicePixelRatio > 1) {
            svgElement.style.imageRendering = 'crisp-edges';
        }

        console.log(`🎨 تم تحسين دقة SVG للتكبير ${(zoom * 100).toFixed(0)}%`);

    } catch (error) {
        console.warn('⚠️ خطأ في تحسين دقة SVG:', error);
    }
}

// تبديل قائمة التصميم
function toggleThemeMenu(mermaidId) {
    const dropdown = document.getElementById(`${mermaidId}-theme-menu`);
    if (!dropdown) return;

    // إغلاق جميع القوائم الأخرى
    document.querySelectorAll('.mermaid-theme-dropdown.show').forEach(menu => {
        if (menu.id !== `${mermaidId}-theme-menu`) {
            menu.classList.remove('show');
        }
    });

    // تبديل القائمة الحالية
    dropdown.classList.toggle('show');

    // إغلاق القائمة عند النقر خارجها
    if (dropdown.classList.contains('show')) {
        setTimeout(() => {
            document.addEventListener('click', function closeMenu(e) {
                if (!dropdown.contains(e.target) && !e.target.closest('.theme-btn')) {
                    dropdown.classList.remove('show');
                    document.removeEventListener('click', closeMenu);
                }
            });
        }, 100);
    }
}

// تغيير تصميم المخطط
function changeMermaidTheme(mermaidId, theme) {
    const container = document.querySelector(`[data-mermaid-id="${mermaidId}"]`);
    if (!container) return;

    // إغلاق قائمة التصميم
    const dropdown = document.getElementById(`${mermaidId}-theme-menu`);
    if (dropdown) {
        dropdown.classList.remove('show');
    }

    // تعيين التصميم الجديد
    container.setAttribute('data-theme', theme);

    // إعادة رسم المخطط بالتصميم الجديد
    const diagramElement = document.getElementById(`${mermaidId}-diagram`);
    const codeElement = document.getElementById(`${mermaidId}-code`);

    if (diagramElement && codeElement) {
        const mermaidCode = codeElement.querySelector('code').textContent;

        // تطبيق التصميم على Mermaid
        const mermaidConfig = {
            theme: theme,
            themeVariables: getThemeVariables(theme),
            startOnLoad: false,
            securityLevel: 'loose',
            fontFamily: 'Arial, sans-serif'
        };

        // إعادة تهيئة Mermaid بالتصميم الجديد
        if (window.mermaid) {
            window.mermaid.initialize(mermaidConfig);

            // مسح المحتوى السابق
            diagramElement.innerHTML = '<div class="mermaid-loading">جاري تحميل المخطط...</div>';

            // رسم المخطط الجديد
            setTimeout(() => {
                renderMermaidDiagram(mermaidId, mermaidCode);
            }, 100);
        }
    }

    // إظهار رسالة تأكيد
    showThemeChangeSuccess(theme);
}

// الحصول على متغيرات التصميم
function getThemeVariables(theme) {
    const themes = {
        default: {
            primaryColor: '#4f46e5',
            primaryTextColor: '#ffffff',
            primaryBorderColor: '#3730a3',
            lineColor: '#374151',
            sectionBkgColor: '#f3f4f6',
            altSectionBkgColor: '#e5e7eb',
            gridColor: '#d1d5db',
            secondaryColor: '#10b981',
            tertiaryColor: '#f59e0b'
        },
        dark: {
            primaryColor: '#1f2937',
            primaryTextColor: '#ffffff',
            primaryBorderColor: '#111827',
            lineColor: '#6b7280',
            sectionBkgColor: '#374151',
            altSectionBkgColor: '#4b5563',
            gridColor: '#6b7280',
            secondaryColor: '#10b981',
            tertiaryColor: '#f59e0b'
        },
        forest: {
            primaryColor: '#059669',
            primaryTextColor: '#ffffff',
            primaryBorderColor: '#047857',
            lineColor: '#065f46',
            sectionBkgColor: '#ecfdf5',
            altSectionBkgColor: '#d1fae5',
            gridColor: '#a7f3d0',
            secondaryColor: '#34d399',
            tertiaryColor: '#fbbf24'
        },
        neutral: {
            primaryColor: '#6b7280',
            primaryTextColor: '#ffffff',
            primaryBorderColor: '#4b5563',
            lineColor: '#9ca3af',
            sectionBkgColor: '#f9fafb',
            altSectionBkgColor: '#f3f4f6',
            gridColor: '#e5e7eb',
            secondaryColor: '#8b5cf6',
            tertiaryColor: '#f59e0b'
        },
        base: {
            primaryColor: '#dc2626',
            primaryTextColor: '#ffffff',
            primaryBorderColor: '#b91c1c',
            lineColor: '#991b1b',
            sectionBkgColor: '#fef2f2',
            altSectionBkgColor: '#fee2e2',
            gridColor: '#fca5a5',
            secondaryColor: '#f59e0b',
            tertiaryColor: '#3b82f6'
        }
    };

    return themes[theme] || themes.default;
}

// إظهار رسالة نجاح تغيير التصميم
function showThemeChangeSuccess(theme) {
    const themeNames = {
        default: 'الافتراضي',
        dark: 'الداكن',
        forest: 'الغابة',
        neutral: 'المحايد',
        base: 'الأساسي'
    };

    const message = document.createElement('div');
    message.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: #3b82f6;
        color: white;
        padding: 12px 20px;
        border-radius: 8px;
        font-size: 14px;
        font-weight: 500;
        z-index: 10000;
        box-shadow: 0 4px 12px rgba(0,0,0,0.3);
        animation: slideIn 0.3s ease-out;
    `;

    message.innerHTML = `
        <i class="fas fa-palette" style="margin-left: 8px;"></i>
        تم تطبيق تصميم ${themeNames[theme] || theme}
    `;

    document.body.appendChild(message);

    // إزالة الرسالة بعد 2 ثانية
    setTimeout(() => {
        message.style.animation = 'slideOut 0.3s ease-in';
        setTimeout(() => {
            if (message.parentNode) {
                message.parentNode.removeChild(message);
            }
        }, 300);
    }, 2000);
}

// إزالة عناصر SVG الخطأ من Mermaid
function removeMermaidErrorSVGs() {
    try {
        // البحث عن جميع عناصر SVG التي تحتوي على أخطاء Mermaid
        const errorSVGs = document.querySelectorAll('svg[id*="mermaid-svg-mermaid"]');

        errorSVGs.forEach(svg => {
            // التحقق من وجود نص خطأ في SVG
            const errorText = svg.querySelector('text');
            if (errorText && (
                errorText.textContent.includes('Syntax error') ||
                errorText.textContent.includes('error') ||
                svg.querySelector('.error-icon') ||
                svg.querySelector('.error-text')
            )) {
                console.log('🗑️ إزالة عنصر SVG خطأ من Mermaid:', svg.id);

                // إزالة العنصر من DOM
                if (svg.parentNode) {
                    svg.parentNode.removeChild(svg);
                }
            }
        });

        // إزالة أي عناصر SVG يتيمة (orphaned) في body
        const orphanedSVGs = document.querySelectorAll('body > svg[id*="mermaid"]');
        orphanedSVGs.forEach(svg => {
            const errorText = svg.querySelector('text');
            if (errorText && errorText.textContent.includes('Syntax error')) {
                console.log('🗑️ إزالة عنصر SVG يتيم:', svg.id);
                if (svg.parentNode) {
                    svg.parentNode.removeChild(svg);
                }
            }
        });

    } catch (error) {
        console.error('خطأ في إزالة عناصر SVG الخطأ:', error);
    }
}

// مراقبة إضافة عناصر جديدة للـ DOM لإزالة أخطاء Mermaid فوراً
function observeMermaidErrors() {
    const observer = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
            mutation.addedNodes.forEach(function(node) {
                if (node.nodeType === Node.ELEMENT_NODE) {
                    // التحقق من عناصر SVG المضافة حديثاً
                    if (node.tagName === 'SVG' && node.id && node.id.includes('mermaid-svg-mermaid')) {
                        const errorText = node.querySelector('text');
                        if (errorText && errorText.textContent.includes('Syntax error')) {
                            console.log('🗑️ إزالة عنصر SVG خطأ فور إضافته:', node.id);
                            setTimeout(() => {
                                if (node.parentNode) {
                                    node.parentNode.removeChild(node);
                                }
                            }, 100);
                        }
                    }

                    // البحث في العناصر الفرعية أيضاً
                    const errorSVGs = node.querySelectorAll && node.querySelectorAll('svg[id*="mermaid-svg-mermaid"]');
                    if (errorSVGs) {
                        errorSVGs.forEach(svg => {
                            const errorText = svg.querySelector('text');
                            if (errorText && errorText.textContent.includes('Syntax error')) {
                                console.log('🗑️ إزالة عنصر SVG خطأ في عنصر فرعي:', svg.id);
                                setTimeout(() => {
                                    if (svg.parentNode) {
                                        svg.parentNode.removeChild(svg);
                                    }
                                }, 100);
                            }
                        });
                    }
                }
            });
        });
    });

    // بدء مراقبة التغييرات في DOM
    observer.observe(document.body, {
        childList: true,
        subtree: true
    });

    return observer;
}

// تهيئة Mermaid عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // تأخير قصير للتأكد من تحميل مكتبة Mermaid
    setTimeout(() => {
        initializeMermaid();

        // بدء مراقبة أخطاء Mermaid
        observeMermaidErrors();

        // إزالة أي أخطاء موجودة مسبقاً
        removeMermaidErrorSVGs();

        // تنظيف دوري لعناصر SVG الخطأ كل 5 ثوان
        setInterval(() => {
            removeMermaidErrorSVGs();
        }, 5000);
    }, 500);
});

// إضافة مستمع للنقر في أي مكان لإزالة عناصر SVG الخطأ
document.addEventListener('click', function() {
    setTimeout(() => {
        removeMermaidErrorSVGs();
    }, 100);
});

// إضافة مستمع لتغيير حجم النافذة لإزالة عناصر SVG الخطأ
window.addEventListener('resize', function() {
    setTimeout(() => {
        removeMermaidErrorSVGs();
    }, 200);
});

